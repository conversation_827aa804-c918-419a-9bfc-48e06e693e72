# Tài liệu API Xác thực và Quản lý Người dùng

**Phi<PERSON><PERSON> bản:** 1.0.0
**<PERSON><PERSON><PERSON> cập nhật:** 2025-05-15
**Base URL:** `https://api.yourdomain.com/api`

## Giới thiệu

Mo<PERSON>le này cung cấp các API endpoints để xác thực người dùng, đă<PERSON> ký, đăng nhập, quản lý thông tin cá nhân và cài đặt người dùng.

## Xác thực

Tất cả các API endpoints (trừ đăng ký và đăng nhập) đều yêu cầu xác thực bằng token. Token cần được gửi trong header của mỗi request:

```
Authorization: Bearer 1|laravel_sanctum_token_hash
```

## API Endpoints

### 1. Đ<PERSON>ng ký tài kho<PERSON>n

**Endpoint:** `POST /auth/register`

**Request Body:**
```json
{
  "name": "<PERSON><PERSON><PERSON>",
  "email": "<EMAIL>",
  "password": "password123",
  "password_confirmation": "password123",
  "device_id": "unique-device-identifier",
  "device_name": "Pixel 6"
}
```

**Response (201):**
```json
{
  "user": {
    "id": 1,
    "name": "Nguyen Van A",
    "email": "<EMAIL>",
    "created_at": "2025-05-04T16:18:13.000000Z",
    "updated_at": "2025-05-04T16:18:13.000000Z"
  },
  "token": "1|laravel_sanctum_token_hash",
  "device_id": "unique-device-identifier"
}
```

### 2. Đăng nhập

**Endpoint:** `POST /auth/login`

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "device_id": "unique-device-identifier",
  "device_name": "Pixel 6"
}
```

**Response (200):**
```json
{
  "user": {
    "id": 1,
    "name": "Nguyen Van A",
    "email": "<EMAIL>",
    "avatar": "https://storage.yourdomain.com/avatars/user1.jpg",
    "settings": {
      "theme": "dark",
      "language": "vi",
      "notifications_enabled": true
    }
  },
  "token": "1|laravel_sanctum_token_hash",
  "device_id": "unique-device-identifier"
}
```

### 3. Đăng nhập bằng Google

**Endpoint:** `POST /auth/google`

**Request Body:**
```json
{
  "id_token": "google_id_token",
  "device_id": "unique-device-identifier",
  "device_name": "Pixel 6"
}
```

**Response (200):**
```json
{
  "user": {
    "id": 1,
    "name": "Nguyen Van A",
    "email": "<EMAIL>",
    "avatar": "https://storage.yourdomain.com/avatars/user1.jpg",
    "google_id": "google_user_id",
    "settings": {
      "theme": "dark",
      "language": "vi",
      "notifications_enabled": true
    }
  },
  "token": "1|laravel_sanctum_token_hash",
  "device_id": "unique-device-identifier",
  "is_new_user": false
}
```

### 4. Đăng xuất

**Endpoint:** `POST /auth/logout`

**Request Body:**
```json
{
  "device_id": "unique-device-identifier"
}
```

**Response (200):**
```json
{
  "message": "Đăng xuất thành công"
}
```

### 5. Lấy thông tin người dùng hiện tại

**Endpoint:** `GET /user`

**Response (200):**
```json
{
  "data": {
    "id": 1,
    "name": "Nguyen Van A",
    "email": "<EMAIL>",
    "avatar": "https://storage.yourdomain.com/avatars/user1.jpg",
    "created_at": "2025-05-04T16:18:13.000000Z",
    "updated_at": "2025-05-04T16:18:13.000000Z",
    "settings": {
      "theme": "dark",
      "language": "vi",
      "notifications_enabled": true
    }
  }
}
```

### 6. Đổi mật khẩu

**Endpoint:** `POST /auth/change-password`

**Request Body:**
```json
{
  "current_password": "password123",
  "password": "newpassword123",
  "password_confirmation": "newpassword123"
}
```

**Response (200):**
```json
{
  "message": "Mật khẩu đã được cập nhật"
}
```



### 7. Tìm kiếm người dùng

**Endpoint:** `GET /users/search`

**Query Parameters:**
- `query` (optional): Từ khóa tìm kiếm chung (tìm theo cả tên và email)
- `name` (optional): Tìm kiếm theo tên
- `email` (optional): Tìm kiếm theo email
- `exclude_team` (optional): ID của nhóm mà bạn muốn loại trừ những người đã là thành viên
- `per_page` (optional): Số lượng kết quả trên mỗi trang, mặc định là 15

**Response (200):**
```json
{
  "data": [
    {
      "id": 2,
      "name": "Tran Thi B",
      "email": "<EMAIL>",
      "avatar": "https://storage.yourdomain.com/avatars/user2.jpg",
      "created_at": "2025-05-01T10:00:00.000000Z",
      "updated_at": "2025-05-01T10:00:00.000000Z"
    }
  ],
  "meta": {
    "current_page": 1,
    "last_page": 1,
    "per_page": 15,
    "total": 1
  }
}
```

### 8. Lấy cài đặt người dùng

**Endpoint:** `GET /settings`

**Response (200):**
```json
{
  "data": {
    "theme": "dark",
    "language": "vi",
    "notifications_enabled": true,
    "task_reminders": true,
    "team_invitations": true,
    "team_updates": true,
    "chat_messages": true
  }
}
```

### 11. Cập nhật cài đặt người dùng

**Endpoint:** `PUT /settings`

**Request Body:**
```json
{
  "theme": "light",
  "language": "en",
  "notifications_enabled": true,
  "task_reminders": true,
  "team_invitations": true,
  "team_updates": false,
  "chat_messages": true
}
```

**Response (200):**
```json
{
  "data": {
    "theme": "light",
    "language": "en",
    "notifications_enabled": true,
    "task_reminders": true,
    "team_invitations": true,
    "team_updates": false,
    "chat_messages": true
  }
}
```

## Mã lỗi

| Mã HTTP | Mô tả | Nguyên nhân |
|---------|-------|-------------|
| 400 | Bad Request | Dữ liệu gửi lên không hợp lệ hoặc thiếu thông tin |
| 401 | Unauthorized | Token không hợp lệ hoặc đã hết hạn |
| 403 | Forbidden | Không có quyền thực hiện hành động này |
| 404 | Not Found | Không tìm thấy tài nguyên |
| 422 | Unprocessable Entity | Dữ liệu không vượt qua validation |
| 429 | Too Many Requests | Gửi quá nhiều yêu cầu trong một khoảng thời gian ngắn |
| 500 | Internal Server Error | Lỗi server |
