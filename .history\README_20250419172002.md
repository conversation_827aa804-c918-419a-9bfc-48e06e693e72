# Task Management API

API backend cho ứng dụng quản lý công việ<PERSON> (Task Management) được xây dựng bằng Laravel.

## Y<PERSON>u cầu hệ thống

- PHP >= 8.1
- MySQL >= 5.7
- Composer
- Laravel >= 10.0

## C<PERSON>u trúc dự án

Dự án được tổ chức theo cấu trúc chuẩn của <PERSON>:

- `app/` - Ch<PERSON>a mã nguồn chính của ứng dụng
  - `Http/Controllers/` - Controllers xử lý logic
  - `Models/` - Models tương tác với database
  - `Services/` - Business logic
  - `Repositories/` - Data access layer
- `routes/` - Định nghĩa các routes
  - `api.php` - API routes
  - `web.php` - Web routes
  - `channels.php` - Broadcasting channels
- `config/` - Các file cấu hình
- `database/`
  - `migrations/` - Database migrations
  - `seeders/` - Database seeders
- `tests/` - Unit tests và feature tests
- `storage/` - File uploads, logs, cache
- `resources/` - Views, assets, language files
- `public/` - Public assets

## Công nghệ sử dụng

- Laravel 10 - PHP Framework
- MySQL - Database
- Laravel Sanctum - API Authentication
- PHPUnit - Testing

## Cài đặt

1. Clone repository:
```bash
git clone <repository-url>
cd myapi
```

2. Cài đặt dependencies:
```bash
composer install
```

3. Tạo file .env:
```bash
cp .env.example .env
```

4. Tạo application key:
```bash
php artisan key:generate
```

5. Cấu hình database trong file .env:
```bash
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=myapi
DB_USERNAME=root
DB_PASSWORD=
```

6. Chạy migrations:
```bash
php artisan migrate
```

7. Chạy server:
```bash
php artisan serve
```

## API Endpoints

### Authentication

#### Đăng ký
- **POST** `/api/auth/register`
- Body:
```json
{
    "name": "User Name",
    "email": "<EMAIL>",
    "password": "password",
    "password_confirmation": "password"
}
```

#### Đăng nhập
- **POST** `/api/auth/login`
- Body:
```json
{
    "email": "<EMAIL>",
    "password": "password"
}
```

#### Đăng xuất
- **POST** `/api/auth/logout`
- Header: `Authorization: Bearer {token}`

#### Lấy thông tin user
- **GET** `/api/user`
- Header: `Authorization: Bearer {token}`

### Personal Tasks

#### Lấy danh sách công việc
- **GET** `/api/personal-tasks`
- Header: `Authorization: Bearer {token}`

#### Tạo công việc mới
- **POST** `/api/personal-tasks`
- Header: `Authorization: Bearer {token}`
- Body:
```json
{
    "title": "Task Title",
    "description": "Task Description",
    "deadline": "2024-03-27 15:00:00",
    "priority": 1,
    "status": "pending"
}
```

#### Xem chi tiết công việc
- **GET** `/api/personal-tasks/{taskId}`
- Header: `Authorization: Bearer {token}`

#### Cập nhật công việc
- **PUT** `/api/personal-tasks/{taskId}`
- Header: `Authorization: Bearer {token}`
- Body: (tương tự như tạo mới)

#### Xóa công việc
- **DELETE** `/api/personal-tasks/{taskId}`
- Header: `Authorization: Bearer {token}`

### Teams

#### Lấy danh sách nhóm
- **GET** `/api/teams`
- Header: `Authorization: Bearer {token}`

#### Tạo nhóm mới
- **POST** `/api/teams`
- Header: `Authorization: Bearer {token}`
- Body:
```json
{
    "name": "Team Name",
    "description": "Team Description"
}
```

#### Xem chi tiết nhóm
- **GET** `/api/teams/{teamId}`
- Header: `Authorization: Bearer {token}`

#### Cập nhật nhóm
- **PUT** `/api/teams/{teamId}`
- Header: `Authorization: Bearer {token}`
- Body: (tương tự như tạo mới)

#### Xóa nhóm
- **DELETE** `/api/teams/{teamId}`
- Header: `Authorization: Bearer {token}`

### Team Members

#### Lấy danh sách thành viên
- **GET** `/api/teams/{teamId}/members`
- Header: `Authorization: Bearer {token}`

#### Thêm thành viên
- **POST** `/api/teams/{teamId}/members`
- Header: `Authorization: Bearer {token}`
- Body:
```json
{
    "user_id": 1,
    "role": "member"
}
```

#### Xóa thành viên
- **DELETE** `/api/teams/{teamId}/members/{userId}`
- Header: `Authorization: Bearer {token}`

### Team Tasks

#### Lấy danh sách công việc nhóm
- **GET** `/api/teams/{teamId}/tasks`
- Header: `Authorization: Bearer {token}`

#### Tạo công việc nhóm
- **POST** `/api/teams/{teamId}/tasks`
- Header: `Authorization: Bearer {token}`
- Body:
```json
{
    "title": "Task Title",
    "description": "Task Description",
    "deadline": "2024-03-27 15:00:00",
    "priority": 1,
    "status": "pending"
}
```

#### Xem chi tiết công việc nhóm
- **GET** `/api/teams/{teamId}/tasks/{taskId}`
- Header: `Authorization: Bearer {token}`

#### Cập nhật công việc nhóm
- **PUT** `/api/teams/{teamId}/tasks/{taskId}`
- Header: `Authorization: Bearer {token}`
- Body: (tương tự như tạo mới)

#### Xóa công việc nhóm
- **DELETE** `/api/teams/{teamId}/tasks/{taskId}`
- Header: `Authorization: Bearer {token}`

### Team Task Assignments

#### Lấy danh sách phân công
- **GET** `/api/teams/{teamId}/tasks/{taskId}/assignments`
- Header: `Authorization: Bearer {token}`

#### Giao công việc
- **POST** `/api/teams/{teamId}/tasks/{taskId}/assignments`
- Header: `Authorization: Bearer {token}`
- Body:
```json
{
    "user_id": 1,
    "status": "pending"
}
```

#### Cập nhật tiến độ
- **PUT** `/api/teams/{teamId}/tasks/{taskId}/assignments/{assignmentId}`
- Header: `Authorization: Bearer {token}`
- Body:
```json
{
    "status": "in_progress",
    "progress": 50
}
```

#### Xóa phân công
- **DELETE** `/api/teams/{teamId}/tasks/{taskId}/assignments/{assignmentId}`
- Header: `Authorization: Bearer {token}`

### Group Chat

#### Lấy danh sách tin nhắn
- **GET** `/api/teams/{teamId}/chat`
- Header: `Authorization: Bearer {token}`

#### Gửi tin nhắn
- **POST** `/api/teams/{teamId}/chat`
- Header: `Authorization: Bearer {token}`
- Body:
```json
{
    "message": "Message content"
}
```

#### Đánh dấu tin nhắn đã đọc
- **PUT** `/api/teams/{teamId}/chat/{messageId}/read`
- Header: `Authorization: Bearer {token}`

#### Lấy số lượng tin nhắn chưa đọc
- **GET** `/api/teams/{teamId}/chat/unread`
- Header: `Authorization: Bearer {token}`
- Response:
```json
{
    "unread_count": 5
}
```

## Response Format

Tất cả các response đều có format JSON với cấu trúc:

```json
{
    "data": {}, // hoặc []
    "message": "Success message", // tùy trường hợp
    "status": 200 // HTTP status code
}
```

## Error Handling

Khi có lỗi, API sẽ trả về response với format:

```json
{
    "message": "Error message",
    "errors": {
        "field": ["Error detail"]
    },
    "status": 400 // HTTP status code
}
```

## Authentication

API sử dụng Laravel Sanctum cho authentication. Sau khi đăng nhập thành công, server sẽ trả về token. Client cần gửi token này trong header của mọi request:

```
Authorization: Bearer {token}
```

## Rate Limiting

API có giới hạn 60 requests/phút cho mỗi IP address.

## Google Authentication

### Backend Setup (Laravel)

Dự án này đã được tích hợp với Google OAuth để hỗ trợ xác thực người dùng thông qua tài khoản Google, đặc biệt là cho ứng dụng Android.

#### Cấu hình

1. File `.env` cần được cấu hình:
```
GOOGLE_CLIENT_ID=your-client-id-from-google-console
```

2. API Endpoints cho Google Auth:
   - **POST** `/api/auth/google` - Đăng nhập bằng Google
   - **POST** `/api/auth/google/link` - Liên kết tài khoản với Google
   - **POST** `/api/auth/google/unlink` - Hủy liên kết tài khoản với Google
   - **POST** `/api/auth/set-password` - Đặt mật khẩu cho tài khoản đăng nhập bằng Google

#### Luồng xác thực

Backend xử lý ID Token từ Google theo luồng sau:
1. Nhận ID Token từ Android app
2. Giải mã JWT payload để lấy thông tin người dùng
3. Tìm hoặc tạo người dùng trong hệ thống
4. Tạo Sanctum token và trả về cho client

### Android Implementation (Jetpack Compose)

#### Cài đặt

1. Thêm dependencies vào `build.gradle` (app-level):

```gradle
dependencies {
    // Google Sign In
    implementation 'com.google.android.gms:play-services-auth:20.7.0'
    
    // Retrofit for API calls
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    
    // Jetpack Compose
    implementation 'androidx.compose.ui:ui:1.5.4'
    implementation 'androidx.compose.material3:material3:1.1.2'
    implementation 'androidx.compose.ui:ui-tooling-preview:1.5.4'
    implementation 'androidx.activity:activity-compose:1.8.1'
    
    // Accompanist for permissions
    implementation 'com.google.accompanist:accompanist-permissions:0.32.0'
    
    // ViewModel with Compose
    implementation 'androidx.lifecycle:lifecycle-viewmodel-compose:2.6.2'
}
```

2. Thêm cấu hình vào `AndroidManifest.xml`:

```xml
<manifest ...>
    <uses-permission android:name="android.permission.INTERNET" />
    <!-- ... -->
</manifest>
```

#### Thiết lập Google Sign-In

1. Lấy SHA-1 fingerprint từ Android Studio:
   ```
   Task: Android > signingReport
   ```

2. Thêm fingerprint vào Google Cloud Console:
   - Vào https://console.cloud.google.com/
   - Chọn project của bạn
   - Vào "APIs & Services" > "Credentials"
   - Chọn "Android" application type
   - Thêm package name và SHA-1 certificate fingerprint

3. Tạo file cấu hình API cho Retrofit:

```kotlin
interface ApiService {
    @POST("auth/google")
    suspend fun googleSignIn(@Body request: GoogleAuthRequest): ApiResponse<AuthData>
    
    @POST("auth/google/link")
    suspend fun linkGoogleAccount(@Body request: GoogleAuthRequest): ApiResponse<UserData>
    
    @POST("auth/google/unlink")
    suspend fun unlinkGoogleAccount(): ApiResponse<String>
    
    @POST("auth/set-password")
    suspend fun setPassword(@Body request: SetPasswordRequest): ApiResponse<String>
}

data class GoogleAuthRequest(val id_token: String)
data class SetPasswordRequest(val password: String, val password_confirmation: String)
data class ApiResponse<T>(val data: T, val message: String)
data class AuthData(val user: UserData, val token: String)
data class UserData(val id: Int, val name: String, val email: String, val avatar: String?)
```

4. Tạo một GoogleAuthViewModel:

```kotlin
class GoogleAuthViewModel(private val apiService: ApiService) : ViewModel() {
    
    private val _googleSignInClient = MutableStateFlow<GoogleSignInClient?>(null)
    val googleSignInClient: StateFlow<GoogleSignInClient?> = _googleSignInClient
    
    private val _authState = MutableStateFlow<AuthState>(AuthState.Idle)
    val authState: StateFlow<AuthState> = _authState
    
    init {
        setupGoogleSignIn()
    }
    
    private fun setupGoogleSignIn() {
        val gso = GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
            .requestIdToken("YOUR_CLIENT_ID_HERE")
            .requestEmail()
            .build()
            
        _googleSignInClient.value = GoogleSignIn.getClient(YourApplication.context, gso)
    }
    
    fun handleSignInResult(completedTask: Task<GoogleSignInAccount>) {
        try {
            val account = completedTask.getResult(ApiException::class.java)
            authenticateWithBackend(account.idToken!!)
        } catch (e: ApiException) {
            _authState.value = AuthState.Error("Google Sign In Failed: ${e.message}")
        }
    }
    
    private fun authenticateWithBackend(idToken: String) {
        viewModelScope.launch {
            _authState.value = AuthState.Loading
            try {
                val response = apiService.googleSignIn(GoogleAuthRequest(idToken))
                saveAuthToken(response.data.token)
                _authState.value = AuthState.Authenticated(response.data.user)
            } catch (e: Exception) {
                _authState.value = AuthState.Error("Authentication Failed: ${e.message}")
            }
        }
    }
    
    fun linkGoogleAccount(idToken: String) {
        viewModelScope.launch {
            _authState.value = AuthState.Loading
            try {
                val response = apiService.linkGoogleAccount(GoogleAuthRequest(idToken))
                _authState.value = AuthState.Updated(response.data)
            } catch (e: Exception) {
                _authState.value = AuthState.Error("Linking Failed: ${e.message}")
            }
        }
    }
    
    fun unlinkGoogleAccount() {
        viewModelScope.launch {
            _authState.value = AuthState.Loading
            try {
                val response = apiService.unlinkGoogleAccount()
                _authState.value = AuthState.Success(response.message)
            } catch (e: Exception) {
                _authState.value = AuthState.Error("Unlinking Failed: ${e.message}")
            }
        }
    }
    
    fun setPassword(password: String, passwordConfirmation: String) {
        viewModelScope.launch {
            _authState.value = AuthState.Loading
            try {
                val response = apiService.setPassword(
                    SetPasswordRequest(password, passwordConfirmation)
                )
                _authState.value = AuthState.Success(response.message)
            } catch (e: Exception) {
                _authState.value = AuthState.Error("Setting Password Failed: ${e.message}")
            }
        }
    }
    
    private fun saveAuthToken(token: String) {
        // Save token to SharedPreferences or DataStore
    }
}

sealed class AuthState {
    object Idle : AuthState()
    object Loading : AuthState()
    data class Authenticated(val user: UserData) : AuthState()
    data class Updated(val user: UserData) : AuthState()
    data class Success(val message: String) : AuthState()
    data class Error(val message: String) : AuthState()
}
```

#### Tạo Google Sign-In Button trong Jetpack Compose

```kotlin
@Composable
fun GoogleSignInButton(
    viewModel: GoogleAuthViewModel,
    onSignInResult: (ActivityResult) -> Unit
) {
    val context = LocalContext.current
    val launcher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        onSignInResult(result)
    }
    
    val googleSignInClient by viewModel.googleSignInClient.collectAsState()
    
    Button(
        onClick = {
            googleSignInClient?.let {
                launcher.launch(it.signInIntent)
            }
        },
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        colors = ButtonDefaults.buttonColors(containerColor = MaterialTheme.colorScheme.surface),
        border = BorderStroke(1.dp, MaterialTheme.colorScheme.outline)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_google),
                contentDescription = "Google icon",
                modifier = Modifier.size(24.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "Sign in with Google",
                color = MaterialTheme.colorScheme.onSurface
            )
        }
    }
}
```

#### Xử lý kết quả đăng nhập

Trong Activity hoặc Composable chính:

```kotlin
@Composable
fun AuthScreen(viewModel: GoogleAuthViewModel) {
    val authState by viewModel.authState.collectAsState()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        when (authState) {
            is AuthState.Idle -> {
                GoogleSignInButton(
                    viewModel = viewModel
                ) { result ->
                    if (result.resultCode == Activity.RESULT_OK) {
                        val task = GoogleSignIn.getSignedInAccountFromIntent(result.data)
                        viewModel.handleSignInResult(task)
                    }
                }
            }
            is AuthState.Loading -> {
                CircularProgressIndicator()
            }
            is AuthState.Authenticated -> {
                val user = (authState as AuthState.Authenticated).user
                Text("Welcome, ${user.name}")
                // Navigate to main screen
            }
            is AuthState.Error -> {
                val error = (authState as AuthState.Error).message
                Text(
                    text = error,
                    color = MaterialTheme.colorScheme.error
                )
                GoogleSignInButton(
                    viewModel = viewModel
                ) { result ->
                    if (result.resultCode == Activity.RESULT_OK) {
                        val task = GoogleSignIn.getSignedInAccountFromIntent(result.data)
                        viewModel.handleSignInResult(task)
                    }
                }
            }
            else -> {}
        }
    }
}
```

#### Liên kết/Hủy liên kết tài khoản Google

Liên kết tài khoản:

```kotlin
@Composable
fun LinkGoogleAccountScreen(viewModel: GoogleAuthViewModel) {
    val authState by viewModel.authState.collectAsState()
    val context = LocalContext.current
    val launcher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val task = GoogleSignIn.getSignedInAccountFromIntent(result.data)
            try {
                val account = task.getResult(ApiException::class.java)
                account.idToken?.let { viewModel.linkGoogleAccount(it) }
            } catch (e: ApiException) {
                Toast.makeText(context, "Google Sign In Failed", Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    val googleSignInClient by viewModel.googleSignInClient.collectAsState()
    
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Button(
            onClick = {
                googleSignInClient?.let {
                    launcher.launch(it.signInIntent)
                }
            }
        ) {
            Text("Link Google Account")
        }
        
        when (authState) {
            is AuthState.Loading -> CircularProgressIndicator()
            is AuthState.Updated -> {
                Text(
                    "Google Account Linked Successfully",
                    color = MaterialTheme.colorScheme.primary
                )
            }
            is AuthState.Error -> {
                Text(
                    text = (authState as AuthState.Error).message,
                    color = MaterialTheme.colorScheme.error
                )
            }
            else -> {}
        }
    }
}
```

Hủy liên kết tài khoản:

```kotlin
@Composable
fun UnlinkGoogleAccountScreen(viewModel: GoogleAuthViewModel) {
    val authState by viewModel.authState.collectAsState()
    
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Button(
            onClick = { viewModel.unlinkGoogleAccount() }
        ) {
            Text("Unlink Google Account")
        }
        
        when (authState) {
            is AuthState.Loading -> CircularProgressIndicator()
            is AuthState.Success -> {
                Text(
                    (authState as AuthState.Success).message,
                    color = MaterialTheme.colorScheme.primary
                )
            }
            is AuthState.Error -> {
                Text(
                    text = (authState as AuthState.Error).message,
                    color = MaterialTheme.colorScheme.error
                )
            }
            else -> {}
        }
    }
}
```

#### Đặt mật khẩu cho tài khoản Google

```kotlin
@Composable
fun SetPasswordScreen(viewModel: GoogleAuthViewModel) {
    val authState by viewModel.authState.collectAsState()
    var password by remember { mutableStateOf("") }
    var confirmPassword by remember { mutableStateOf("") }
    
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        OutlinedTextField(
            value = password,
            onValueChange = { password = it },
            label = { Text("Password") },
            visualTransformation = PasswordVisualTransformation(),
            modifier = Modifier.fillMaxWidth()
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        OutlinedTextField(
            value = confirmPassword,
            onValueChange = { confirmPassword = it },
            label = { Text("Confirm Password") },
            visualTransformation = PasswordVisualTransformation(),
            modifier = Modifier.fillMaxWidth()
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Button(
            onClick = { viewModel.setPassword(password, confirmPassword) },
            enabled = password.length >= 8 && password == confirmPassword
        ) {
            Text("Set Password")
        }
        
        when (authState) {
            is AuthState.Loading -> CircularProgressIndicator()
            is AuthState.Success -> {
                Text(
                    (authState as AuthState.Success).message,
                    color = MaterialTheme.colorScheme.primary
                )
            }
            is AuthState.Error -> {
                Text(
                    text = (authState as AuthState.Error).message,
                    color = MaterialTheme.colorScheme.error
                )
            }
            else -> {}
        }
    }
}
```

### Lưu ý bảo mật

1. **Server-side validation**: Backend luôn xác minh tính hợp lệ của ID token từ client
2. **Secure password storage**: Mật khẩu người dùng được mã hóa bằng bcrypt
3. **Token expiration**: Sanctum tokens có thời hạn để tăng bảo mật
4. **Account linking security**: Kiểm tra xác thực trước khi cho phép liên kết/hủy liên kết tài khoản
5. **Android**: Lưu trữ token an toàn trong EncryptedSharedPreferences

### Lưu ý khi triển khai

1. **Signing keys**: Cần sử dụng cùng một ứng dụng Android (cùng signing key) cho testing và production
2. **Release SHA-1**: Thêm SHA-1 của signing key release vào Google Cloud Console trước khi phát hành app
3. **Error handling**: Xử lý các lỗi một cách rõ ràng và thân thiện với người dùng
4. **Firewall rules**: Đảm bảo server cho phép các kết nối từ Google API (khi cần thiết)

## Testing

### Backend Testing

// ... existing code ...
