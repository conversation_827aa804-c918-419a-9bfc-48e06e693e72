# API Quản lý Công việc và Chat Nhóm

API này được xây dựng trên nền tả<PERSON>, cung cấp các endpoint và chiến lược cho ứng dụng Android có khả năng:
- Qu<PERSON>n lý công việc cá nhân và nhóm
- Chat nhóm realtime
- Đồng bộ dữ liệu giữa thiết bị và server
- Hỗ trợ hoạt động offline
- Thông báo đẩy

## 1. Kiến trúc tổng quan

### 1.1 Stack công nghệ
- **Backend**: <PERSON>vel, MySQL
- **WebSocket**: <PERSON><PERSON> Reverb
- **API Authentication**: Sanctum Token-based

### 1.2 Luồng dữ liệu
- **Realtime**: WebSocket cho chat, typing indicator, read status
- **Đồng bộ**: RESTful API để đồng bộ dữ liệu
- **Thông báo**: API polling để lấy thông báo

## 2. <PERSON><PERSON><PERSON> trúc dữ liệu

### 2.1 B<PERSON>ng cần đồng bộ (local và server)

| Bảng | Mô tả | Đồng bộ | Chiến lược |
|------|-------|---------|------------|
| `personal_tasks` | Công việc cá nhân | ✅ | Đồng bộ hai chiều |
| `group_chat_messages` | Tin nhắn nhóm | ✅ | Push lên server, WebSocket xuống thiết bị |
| `message_read_status` | Trạng thái đọc | ✅ | Đồng bộ hai chiều |
| `message_reactions` | Phản ứng emoji | ✅ | Đồng bộ hai chiều |
| `team_tasks` | Công việc nhóm | ✅ | Đồng bộ hai chiều (chỉ những công việc được phân công) |

### 2.2 Bảng chỉ lưu trên server

| Bảng | Mô tả |
|------|-------|
| `users` | Thông tin người dùng |
| `teams` | Thông tin nhóm |
| `team_members` | Thành viên nhóm |
| `team_task_assignments` | Phân công công việc |
| `devices` | Thiết bị đăng ký |
| `notification_queues` | Hàng đợi thông báo |
| `sync_status` | Trạng thái đồng bộ thiết bị |

## 3. API Endpoints

### 3.1 Xác thực
- `POST /api/auth/register`: Đăng ký tài khoản
- `POST /api/auth/login`: Đăng nhập
- `POST /api/auth/google`: Đăng nhập bằng Google
- `POST /api/auth/logout`: Đăng xuất
- `GET /api/user`: Lấy thông tin người dùng

### 3.2 Công việc cá nhân
- `GET /api/personal-tasks`: Danh sách công việc
- `POST /api/personal-tasks`: Tạo mới 
- `GET /api/personal-tasks/{id}`: Chi tiết
- `PUT /api/personal-tasks/{id}`: Cập nhật
- `DELETE /api/personal-tasks/{id}`: Xóa

### 3.3 Nhóm
- `GET /api/teams`: Danh sách nhóm
- `POST /api/teams`: Tạo nhóm
- `GET /api/teams/{id}`: Chi tiết nhóm
- `PUT /api/teams/{id}`: Cập nhật nhóm
- `DELETE /api/teams/{id}`: Xóa nhóm

### 3.4 Thành viên nhóm
- `GET /api/teams/{team}/members`: Danh sách thành viên
- `POST /api/teams/{team}/members`: Thêm thành viên
- `DELETE /api/teams/{team}/members/{user}`: Xóa thành viên

### 3.5 Công việc nhóm
- `GET /api/teams/{team}/tasks`: Danh sách công việc
- `POST /api/teams/{team}/tasks`: Tạo mới
- `GET /api/teams/{team}/tasks/{task}`: Chi tiết
- `PUT /api/teams/{team}/tasks/{task}`: Cập nhật
- `DELETE /api/teams/{team}/tasks/{task}`: Xóa

### 3.6 Phân công công việc
- `GET /api/teams/{team}/tasks/{task}/assignments`: Danh sách phân công
- `POST /api/teams/{team}/tasks/{task}/assignments`: Phân công mới
- `PUT /api/teams/{team}/tasks/{task}/assignments/{assignment}`: Cập nhật
- `DELETE /api/teams/{team}/tasks/{task}/assignments/{assignment}`: Hủy

### 3.7 Chat
- `GET /api/teams/{team}/chat`: Lấy lịch sử chat
- `POST /api/teams/{team}/chat`: Gửi tin nhắn
- `PUT /api/teams/{team}/chat/{message}/read`: Đánh dấu đã đọc
- `GET /api/teams/{team}/chat/unread`: Đếm tin chưa đọc
- `POST /api/teams/{team}/chat/typing`: Cập nhật trạng thái nhập
- `POST /api/teams/{team}/chat/retry/{clientTempId}`: Gửi lại tin nhắn
- `PUT /api/teams/{team}/chat/{message}`: Chỉnh sửa tin nhắn
- `DELETE /api/teams/{team}/chat/{message}`: Xóa tin nhắn
- `POST /api/teams/{team}/chat/{message}/react`: Thêm/xóa phản ứng

### 3.8 Tệp tin
- `POST /api/upload`: Tải lên tệp tin

### 3.9 Đồng bộ hóa
- `POST /api/sync/initial`: Đồng bộ lần đầu
- `POST /api/sync/quick`: Đồng bộ nhanh
- `POST /api/sync/push`: Đẩy dữ liệu local lên server

### 3.10 Thông báo
- `POST /api/notifications/register-device`: Đăng ký thiết bị
- `DELETE /api/notifications/unregister-device`: Hủy đăng ký
- `GET /api/notifications`: Lấy thông báo mới

## 4. Cơ chế WebSocket

### 4.1 Kết nối
```
wss://your-api.com/reverb?token={access_token}
```

### 4.2 Kênh
- `private-teams.{teamId}`: Kênh riêng cho mỗi nhóm

### 4.3 Events
- `new-chat-message`: Tin nhắn mới
- `message-read`: Tin nhắn đã đọc
- `user-typing`: Người dùng đang nhập
- `message-reaction-updated`: Cập nhật phản ứng
- `message-updated`: Tin nhắn được chỉnh sửa
- `message-deleted`: Tin nhắn bị xóa

## 5. Hướng dẫn triển khai Android (Jetpack Compose)

### 5.1 Cấu trúc dự án đề xuất

```
app/
├── data/
│   ├── api/            # API Service interface và models
│   ├── database/       # Room entities và DAOs
│   ├── repository/     # Repository pattern implementation
│   └── websocket/      # WebSocket manager
├── di/                 # Dependency Injection
├── domain/             # Business logic và use cases
│   ├── model/          # Domain models
│   ├── repository/     # Repository interfaces
│   └── usecase/        # Use cases
├── ui/                 # UI components
│   ├── auth/           # Đăng nhập & Đăng ký
│   ├── chat/           # Chat screen
│   ├── personal/       # Công việc cá nhân
│   ├── teams/          # Quản lý nhóm
│   ├── team_tasks/     # Công việc nhóm
│   └── theme/          # UI theme
├── utils/              # Utilities
└── workers/            # WorkManager tasks
```

### 5.2 Các thành phần chính

#### 5.2.1 Room Database
```kotlin
@Database(
    entities = [
        UserEntity::class,
        PersonalTaskEntity::class,
        TeamEntity::class,
        TeamTaskEntity::class,
        MessageEntity::class,
        MessageReadStatusEntity::class,
        MessageReactionEntity::class
    ],
    version = 1
)
abstract class AppDatabase : RoomDatabase() {
    abstract fun userDao(): UserDao
    abstract fun personalTaskDao(): PersonalTaskDao
    abstract fun teamDao(): TeamDao
    abstract fun teamTaskDao(): TeamTaskDao
    abstract fun messageDao(): MessageDao
    abstract fun messageReadStatusDao(): MessageReadStatusDao
    abstract fun messageReactionDao(): MessageReactionDao
}
```

#### 5.2.2 API Service
```kotlin
interface ApiService {
    // Auth
    @POST("auth/login")
    suspend fun login(@Body loginRequest: LoginRequest): Response<AuthResponse>
    
    // Tasks
    @GET("personal-tasks")
    suspend fun getPersonalTasks(): Response<List<PersonalTask>>
    
    // Teams
    @GET("teams")
    suspend fun getTeams(): Response<List<Team>>
    
    // Chat
    @GET("teams/{teamId}/chat")
    suspend fun getMessages(@Path("teamId") teamId: Long): Response<List<Message>>
    
    // Sync
    @POST("sync/initial")
    suspend fun initialSync(@Body request: InitialSyncRequest): Response<InitialSyncResponse>
    
    @POST("sync/quick")
    suspend fun quickSync(@Body request: QuickSyncRequest): Response<QuickSyncResponse>
    
    @POST("sync/push")
    suspend fun pushChanges(@Body request: PushRequest): Response<PushResponse>
}
```

#### 5.2.3 WebSocket Manager
```kotlin
class WebSocketManager(
    private val serverUrl: String,
    private val authToken: String,
    private val messageRepository: MessageRepository,
    private val scope: CoroutineScope
) {
    private var webSocket: WebSocket? = null
    private val _connectionState = MutableStateFlow(ConnectionState.DISCONNECTED)
    val connectionState = _connectionState.asStateFlow()
    
    private val _events = MutableSharedFlow<ChatEvent>()
    val events = _events.asSharedFlow()
    
    fun connect(teamId: Long) {
        val request = Request.Builder()
            .url("$serverUrl/reverb?token=$authToken")
            .build()
            
        webSocket = OkHttpClient().newWebSocket(request, createWebSocketListener(teamId))
        _connectionState.value = ConnectionState.CONNECTING
    }
    
    private fun createWebSocketListener(teamId: Long): WebSocketListener {
        return object : WebSocketListener() {
            override fun onOpen(webSocket: WebSocket, response: Response) {
                _connectionState.value = ConnectionState.CONNECTED
                
                // Subscribe to channel
                webSocket.send(
                    JSONObject().apply {
                        put("event", "subscribe")
                        put("channel", "private-teams.$teamId")
                    }.toString()
                )
            }
            
            override fun onMessage(webSocket: WebSocket, text: String) {
                scope.launch {
                    processMessage(text)
                }
            }
            
            override fun onClosed(webSocket: WebSocket, code: Int, reason: String) {
                _connectionState.value = ConnectionState.DISCONNECTED
            }
            
            override fun onFailure(webSocket: WebSocket, t: Throwable, response: Response?) {
                _connectionState.value = ConnectionState.ERROR
                // Implement retry logic
            }
        }
    }
    
    private suspend fun processMessage(text: String) {
        try {
            val json = JSONObject(text)
            val eventName = json.optString("event")
            val data = json.optJSONObject("data")
            
            when (eventName) {
                "new-chat-message" -> {
                    val message = parseMessage(data)
                    messageRepository.saveMessage(message)
                    _events.emit(ChatEvent.NewMessage(message))
                }
                "message-read" -> {
                    val readStatus = parseReadStatus(data)
                    messageRepository.saveReadStatus(readStatus)
                    _events.emit(ChatEvent.MessageRead(readStatus))
                }
                // Handle other events...
            }
        } catch (e: Exception) {
            Log.e("WebSocketManager", "Error processing message", e)
        }
    }
    
    fun disconnect() {
        webSocket?.close(1000, "User disconnected")
        webSocket = null
        _connectionState.value = ConnectionState.DISCONNECTED
    }
    
    fun sendTypingStatus(teamId: Long, isTyping: Boolean) {
        val json = JSONObject().apply {
            put("event", "typing")
            put("channel", "private-teams.$teamId")
            put("data", JSONObject().apply {
                put("is_typing", isTyping)
            })
        }
        webSocket?.send(json.toString())
    }
}

sealed class ChatEvent {
    data class NewMessage(val message: Message) : ChatEvent()
    data class MessageRead(val readStatus: ReadStatus) : ChatEvent()
    data class UserTyping(val userId: Long, val isTyping: Boolean) : ChatEvent()
    data class MessageReaction(val messageId: Long, val userId: Long, val reaction: String, val action: String) : ChatEvent()
    data class MessageUpdated(val message: Message) : ChatEvent()
    data class MessageDeleted(val messageId: Long) : ChatEvent()
}

enum class ConnectionState {
    DISCONNECTED, CONNECTING, CONNECTED, ERROR
}
```

#### 5.2.4 Synchronization Manager
```kotlin
@HiltViewModel
class SyncViewModel @Inject constructor(
    private val syncRepository: SyncRepository,
    private val connectivityObserver: ConnectivityObserver
) : ViewModel() {

    private val _syncState = MutableStateFlow<SyncState>(SyncState.Idle)
    val syncState = _syncState.asStateFlow()
    
    init {
        // Observe network connectivity
        viewModelScope.launch {
            connectivityObserver.observe().collect { status ->
                when (status) {
                    ConnectionStatus.Available -> {
                        // Auto-sync when connection becomes available
                        if (syncRepository.hasPendingChanges()) {
                            syncData()
                        }
                    }
                    else -> {}
                }
            }
        }
    }
    
    fun syncData() {
        viewModelScope.launch {
            _syncState.value = SyncState.Syncing
            
            try {
                // First push local changes
                syncRepository.pushLocalChanges()
                
                // Then fetch new changes
                syncRepository.quickSync()
                
                _syncState.value = SyncState.Success(System.currentTimeMillis())
            } catch (e: Exception) {
                _syncState.value = SyncState.Error(e.message ?: "Unknown error")
            }
        }
    }
    
    fun performInitialSync() {
        viewModelScope.launch {
            _syncState.value = SyncState.Syncing
            
            try {
                syncRepository.initialSync()
                _syncState.value = SyncState.Success(System.currentTimeMillis())
            } catch (e: Exception) {
                _syncState.value = SyncState.Error(e.message ?: "Unknown error")
            }
        }
    }
}

sealed class SyncState {
    object Idle : SyncState()
    object Syncing : SyncState()
    data class Success(val timestamp: Long) : SyncState()
    data class Error(val message: String) : SyncState()
}
```

### 5.3 UI Components (Jetpack Compose)

#### 5.3.1 Chat Screen
```kotlin
@Composable
fun ChatScreen(
    viewModel: ChatViewModel = hiltViewModel(),
    teamId: Long
) {
    val messageState = viewModel.messages.collectAsState()
    val connectionState = viewModel.connectionState.collectAsState()
    val typingUsers = viewModel.typingUsers.collectAsState()
    
    LaunchedEffect(teamId) {
        viewModel.loadMessages(teamId)
        viewModel.connectWebSocket(teamId)
    }
    
    DisposableEffect(Unit) {
        onDispose {
            viewModel.disconnectWebSocket()
        }
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Team Chat") },
                actions = {
                    // Status indicator
                    when (connectionState.value) {
                        ConnectionState.CONNECTED -> 
                            Icon(
                                Icons.Filled.Check, 
                                contentDescription = "Connected",
                                tint = Color.Green
                            )
                        ConnectionState.CONNECTING -> 
                            CircularProgressIndicator(
                                modifier = Modifier.size(24.dp),
                                strokeWidth = 2.dp
                            )
                        else -> 
                            Icon(
                                Icons.Filled.Warning, 
                                contentDescription = "Disconnected",
                                tint = Color.Red
                            )
                    }
                }
            )
        }
    ) { padding ->
        Column(
            modifier = Modifier
                .padding(padding)
                .fillMaxSize()
        ) {
            // Messages list
            LazyColumn(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
                reverseLayout = true
            ) {
                items(messageState.value) { message ->
                    MessageItem(
                        message = message,
                        currentUserId = viewModel.currentUserId,
                        onReactionClick = { messageId, reaction -> 
                            viewModel.toggleReaction(messageId, reaction)
                        },
                        onLongClick = { message ->
                            // Show options menu
                        }
                    )
                }
            }
            
            // Typing indicator
            if (typingUsers.value.isNotEmpty()) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 4.dp)
                ) {
                    Text(
                        text = buildAnnotatedString {
                            typingUsers.value.forEachIndexed { index, user ->
                                if (index > 0) append(", ")
                                append(user.name)
                            }
                            append(" is typing...")
                        },
                        style = MaterialTheme.typography.caption,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f)
                    )
                }
            }
            
            // Input field
            ChatInput(
                onMessageSent = { text ->
                    viewModel.sendMessage(teamId, text)
                },
                onTypingStateChanged = { isTyping ->
                    viewModel.updateTypingStatus(teamId, isTyping)
                },
                onAttachmentClick = {
                    // Open attachment picker
                }
            )
        }
    }
}

@Composable
fun MessageItem(
    message: Message,
    currentUserId: Long,
    onReactionClick: (Long, String) -> Unit,
    onLongClick: (Message) -> Unit
) {
    val isOwnMessage = message.senderId == currentUserId
    
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(8.dp),
        horizontalAlignment = if (isOwnMessage) Alignment.End else Alignment.Start
    ) {
        Row(
            modifier = Modifier
                .combinedClickable(
                    onClick = { /* Single click action */ },
                    onLongClick = { onLongClick(message) }
                )
        ) {
            if (!isOwnMessage) {
                // Avatar
                Box(
                    modifier = Modifier
                        .size(36.dp)
                        .clip(CircleShape)
                        .background(MaterialTheme.colors.primary.copy(alpha = 0.2f))
                ) {
                    Text(
                        text = message.senderName.first().toString(),
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                Spacer(modifier = Modifier.width(8.dp))
            }
            
            // Message content
            Column {
                if (!isOwnMessage) {
                    Text(
                        text = message.senderName,
                        style = MaterialTheme.typography.caption
                    )
                    Spacer(modifier = Modifier.height(4.dp))
                }
                
                Surface(
                    color = if (isOwnMessage) 
                        MaterialTheme.colors.primary 
                    else 
                        MaterialTheme.colors.surface,
                    shape = RoundedCornerShape(8.dp),
                    elevation = 1.dp
                ) {
                    Column(modifier = Modifier.padding(8.dp)) {
                        // Message text
                        Text(
                            text = message.message,
                            color = if (isOwnMessage) 
                                MaterialTheme.colors.onPrimary 
                            else 
                                MaterialTheme.colors.onSurface
                        )
                        
                        // File attachment if any
                        message.fileUrl?.let { url ->
                            Spacer(modifier = Modifier.height(8.dp))
                            // Render attachment based on type
                            when {
                                url.contains(".jpg") || url.contains(".png") -> {
                                    AsyncImage(
                                        model = url,
                                        contentDescription = "Attachment",
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .height(200.dp)
                                            .clip(RoundedCornerShape(4.dp)),
                                        contentScale = ContentScale.Crop
                                    )
                                }
                                else -> {
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically,
                                        modifier = Modifier
                                            .clip(RoundedCornerShape(4.dp))
                                            .background(MaterialTheme.colors.surface.copy(alpha = 0.3f))
                                            .padding(8.dp)
                                    ) {
                                        Icon(
                                            Icons.Default.AttachFile,
                                            contentDescription = "File"
                                        )
                                        Spacer(modifier = Modifier.width(8.dp))
                                        Text("Attachment")
                                    }
                                }
                            }
                        }
                    }
                }
                
                // Timestamp and status
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = if (isOwnMessage) Arrangement.End else Arrangement.Start
                ) {
                    Text(
                        text = formatDateTime(message.timestamp),
                        style = MaterialTheme.typography.caption.copy(fontSize = 10.sp),
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f)
                    )
                    
                    if (isOwnMessage) {
                        Spacer(modifier = Modifier.width(4.dp))
                        Icon(
                            imageVector = when (message.status) {
                                "sending" -> Icons.Default.Schedule
                                "sent" -> Icons.Default.Check
                                "delivered" -> Icons.Default.DoneAll
                                "failed" -> Icons.Default.Error
                                else -> Icons.Default.Check
                            },
                            contentDescription = message.status,
                            modifier = Modifier.size(14.dp),
                            tint = MaterialTheme.colors.onSurface.copy(alpha = 0.6f)
                        )
                    }
                }
            }
        }
        
        // Reactions
        if (message.reactions.isNotEmpty()) {
            Spacer(modifier = Modifier.height(4.dp))
            Row(
                modifier = Modifier
                    .align(if (isOwnMessage) Alignment.End else Alignment.Start)
                    .padding(horizontal = 8.dp)
            ) {
                message.reactions.forEach { (reaction, users) ->
                    Surface(
                        modifier = Modifier
                            .padding(end = 4.dp)
                            .clickable { onReactionClick(message.id, reaction) },
                        shape = RoundedCornerShape(12.dp),
                        color = if (users.any { it.id == currentUserId })
                            MaterialTheme.colors.primary.copy(alpha = 0.2f)
                        else
                            MaterialTheme.colors.surface,
                        border = BorderStroke(1.dp, MaterialTheme.colors.onSurface.copy(alpha = 0.1f))
                    ) {
                        Row(
                            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(reaction)
                            Spacer(modifier = Modifier.width(4.dp))
                            Text(
                                text = users.size.toString(),
                                style = MaterialTheme.typography.caption
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun ChatInput(
    onMessageSent: (String) -> Unit,
    onTypingStateChanged: (Boolean) -> Unit,
    onAttachmentClick: () -> Unit
) {
    var text by remember { mutableStateOf("") }
    var isTyping by remember { mutableStateOf(false) }
    
    LaunchedEffect(text) {
        val newIsTyping = text.isNotEmpty()
        if (isTyping != newIsTyping) {
            isTyping = newIsTyping
            onTypingStateChanged(newIsTyping)
        }
    }
    
    Surface(
        modifier = Modifier.fillMaxWidth(),
        elevation = 8.dp
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Attachment button
            IconButton(onClick = onAttachmentClick) {
                Icon(Icons.Default.AttachFile, contentDescription = "Attach")
            }
            
            // Text field
            TextField(
                value = text,
                onValueChange = { text = it },
                modifier = Modifier.weight(1f),
                placeholder = { Text("Type a message") },
                colors = TextFieldDefaults.textFieldColors(
                    backgroundColor = Color.Transparent,
                    focusedIndicatorColor = Color.Transparent,
                    unfocusedIndicatorColor = Color.Transparent
                )
            )
            
            // Send button
            IconButton(
                onClick = {
                    if (text.isNotEmpty()) {
                        onMessageSent(text)
                        text = ""
                    }
                },
                enabled = text.isNotEmpty()
            ) {
                Icon(Icons.Default.Send, contentDescription = "Send")
            }
        }
    }
}
```

### 5.4 Background Services

#### 5.4.1 Sync Worker
```kotlin
@HiltWorker
class SyncWorker @AssistedInject constructor(
    @Assisted context: Context,
    @Assisted params: WorkerParameters,
    private val syncRepository: SyncRepository,
    private val connectivityChecker: ConnectivityChecker,
    private val preferences: DataStoreManager
) : CoroutineWorker(context, params) {

    override suspend fun doWork(): Result {
        // Check if we have internet connection
        if (!connectivityChecker.isNetworkAvailable()) {
            return Result.retry()
        }
        
        try {
            // First push local changes
            syncRepository.pushLocalChanges()
            
            // Then fetch new changes
            syncRepository.quickSync()
            
            // Update last sync time
            preferences.updateLastSyncTime(System.currentTimeMillis())
            
            return Result.success()
        } catch (e: Exception) {
            Log.e("SyncWorker", "Sync failed", e)
            return if (runAttemptCount < 3) {
                Result.retry()
            } else {
                Result.failure()
            }
        }
    }
    
    companion object {
        fun schedulePeriodicSync(context: Context) {
            val constraints = Constraints.Builder()
                .setRequiredNetworkType(NetworkType.CONNECTED)
                .setRequiresBatteryNotLow(true)
                .build()
                
            val request = PeriodicWorkRequestBuilder<SyncWorker>(
                15, TimeUnit.MINUTES
            )
                .setConstraints(constraints)
                .build()
                
            WorkManager.getInstance(context)
                .enqueueUniquePeriodicWork(
                    "periodic_sync",
                    ExistingPeriodicWorkPolicy.REPLACE,
                    request
                )
        }
    }
}
```

#### 5.4.2 Notification Worker
```kotlin
@HiltWorker
class NotificationWorker @AssistedInject constructor(
    @Assisted context: Context,
    @Assisted params: WorkerParameters,
    private val notificationRepository: NotificationRepository,
    private val notificationManager: NotificationManagerCompat,
    private val preferences: DataStoreManager
) : CoroutineWorker(context, params) {

    override suspend fun doWork(): Result {
        try {
            val deviceId = preferences.getDeviceId()
            val lastChecked = preferences.getLastNotificationCheck()
            
            val response = notificationRepository.getNotifications(deviceId, lastChecked)
            
            for (notification in response.notifications) {
                showNotification(notification)
            }
            
            preferences.updateLastNotificationCheck(response.serverTime)
            
            return Result.success()
        } catch (e: Exception) {
            Log.e("NotificationWorker", "Failed to fetch notifications", e)
            return Result.retry()
        }
    }
    
    private fun showNotification(notification: NotificationData) {
        val builder = NotificationCompat.Builder(applicationContext, CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle(getTitle(notification))
            .setContentText(getContent(notification))
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setAutoCancel(true)
            
        // Add appropriate intent based on notification type
        when (notification.type) {
            "new_message" -> {
                val teamId = notification.data["team_id"] as Long
                val intent = Intent(applicationContext, MainActivity::class.java).apply {
                    putExtra("DESTINATION", "chat")
                    putExtra("TEAM_ID", teamId)
                }
                val pendingIntent = PendingIntent.getActivity(
                    applicationContext, 
                    notification.id.toInt(), 
                    intent, 
                    PendingIntent.FLAG_IMMUTABLE
                )
                builder.setContentIntent(pendingIntent)
            }
            "task_assignment" -> {
                val teamId = notification.data["team_id"] as Long
                val taskId = notification.data["task_id"] as Long
                val intent = Intent(applicationContext, MainActivity::class.java).apply {
                    putExtra("DESTINATION", "team_task")
                    putExtra("TEAM_ID", teamId)
                    putExtra("TASK_ID", taskId)
                }
                val pendingIntent = PendingIntent.getActivity(
                    applicationContext, 
                    notification.id.toInt(), 
                    intent, 
                    PendingIntent.FLAG_IMMUTABLE
                )
                builder.setContentIntent(pendingIntent)
            }
        }
        
        notificationManager.notify(notification.id.toInt(), builder.build())
    }
    
    private fun getTitle(notification: NotificationData): String {
        return when (notification.type) {
            "new_message" -> {
                val teamName = notification.data["team_name"] as String
                val senderName = notification.data["sender_name"] as String
                "$senderName in $teamName"
            }
            "task_assignment" -> {
                val teamName = notification.data["team_name"] as String
                "New task in $teamName"
            }
            else -> "New notification"
        }
    }
    
    private fun getContent(notification: NotificationData): String {
        return when (notification.type) {
            "new_message" -> notification.data["message_preview"] as String
            "task_assignment" -> notification.data["task_title"] as String
            else -> ""
        }
    }
    
    companion object {
        private const val CHANNEL_ID = "notifications"
        
        fun createNotificationChannel(context: Context) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                val name = "Notifications"
                val descriptionText = "App notifications"
                val importance = NotificationManager.IMPORTANCE_DEFAULT
                val channel = NotificationChannel(CHANNEL_ID, name, importance).apply {
                    description = descriptionText
                }
                val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
                notificationManager.createNotificationChannel(channel)
            }
        }
        
        fun schedulePeriodic(context: Context) {
            val constraints = Constraints.Builder()
                .setRequiredNetworkType(NetworkType.CONNECTED)
                .build()
                
            val request = PeriodicWorkRequestBuilder<NotificationWorker>(
                15, TimeUnit.MINUTES
            )
                .setConstraints(constraints)
                .build()
                
            WorkManager.getInstance(context)
                .enqueueUniquePeriodicWork(
                    "notification_polling",
                    ExistingPeriodicWorkPolicy.REPLACE,
                    request
                )
        }
    }
}
```

## 6. Xử lý offline

### 6.1 Cơ chế hoạt động
- **Local-first**: Lưu trữ và xử lý dữ liệu trên thiết bị trước
- **Optimistic updates**: Cập nhật UI ngay không chờ server
- **Background sync**: Đồng bộ khi có kết nối
- **Conflict resolution**: Server-wins để giải quyết xung đột

### 6.2 Flow xử lý offline
1. **Tạo item mới**:
   - Lưu vào DB local với unique ID tạm thời
   - Đánh dấu item là "chưa đồng bộ"
   - Cập nhật UI ngay lập tức
   - Đưa vào queue để đồng bộ khi có mạng

2. **Cập nhật item**:
   - Cập nhật trong DB local
   - Đánh dấu item là "đã chỉnh sửa, chưa đồng bộ"
   - Đưa vào queue để đồng bộ

3. **Đồng bộ khi có mạng**:
   - Đẩy tất cả item chưa đồng bộ lên server
   - Cập nhật ID tạm thời thành ID thật từ server
   - Đánh dấu item là "đã đồng bộ"

## 7. Xử lý vấn đề khác

### 7.1 Bảo mật
- Sử dụng HTTPS cho mọi request
- Token-based authentication (Laravel Sanctum)
- Lưu token trong EncryptedSharedPreferences
- Auto-refresh token khi cần

### 7.2 Tối ưu hiệu suất
- Pagination cho danh sách và tin nhắn
- Lazy loading hình ảnh và tệp đính kèm
- Cache dữ liệu phù hợp

### 7.3 Xử lý lỗi
- Retry strategy cho API calls
- Exponential backoff cho kết nối WebSocket
- Offline fallback khi không có mạng

## 8. Deployment và thử nghiệm
- Sản xuất: https://api-example.com
- Staging: https://staging-api-example.com

## 9. Liên hệ hỗ trợ
- Admin: <EMAIL>
- Developer: <EMAIL>