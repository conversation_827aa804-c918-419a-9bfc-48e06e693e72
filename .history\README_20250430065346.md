# API Quản lý Công việc và Chat Nhóm

API này được xây dựng trên nền tả<PERSON>, cung cấp các endpoint và chiến lư<PERSON>c cho ứng dụng có khả năng:
- <PERSON>u<PERSON>n lý công việc cá nhân và nhóm
- Chat nhóm realtime
- Đồng bộ dữ liệu giữa thiết bị và server
- Hỗ trợ hoạt động offline
- Thông báo đẩy
- <PERSON><PERSON> tích và báo cáo
- Tích hợp lịch
- Giao diện Kanban
- Qu<PERSON>n lý công việc con (subtasks)
- T<PERSON>y chỉnh giao diện (theme, ngôn ngữ)
- <PERSON><PERSON><PERSON> thự<PERSON> sinh trắc học (Fingerprint/Face Unlock)
- Tự động lưu nháp khi thoát giữa chừng

## 1. <PERSON><PERSON><PERSON> trú<PERSON> tổng quan

### 1.1 Stack công nghệ
- **Backend**: <PERSON><PERSON>, MySQL
- **WebSocket**: <PERSON><PERSON> Reverb
- **API Authentication**: Sanctum Token-based

### 1.2 Luồng dữ liệu
- **Realtime**: WebSocket cho chat, typing indicator, read status
- **Đồng bộ**: RESTful API để đồng bộ dữ liệu
- **Thông báo**: Firebase Cloud Messaging (FCM) cho push notification

## 2. Cấu trúc dữ liệu

### 2.1 Bảng cần đồng bộ (local và server)

| Bảng | Mô tả | Đồng bộ | Chiến lược |
|------|-------|---------|------------|
| `personal_tasks` | Công việc cá nhân | ✅ | Đồng bộ hai chiều |
| `subtasks` | Công việc con | ✅ | Đồng bộ hai chiều |
| `messages` | Tin nhắn nhóm | ✅ | Push lên server, WebSocket xuống thiết bị |
| `message_read_status` | Trạng thái đọc | ✅ | Đồng bộ hai chiều |
| `message_reactions` | Phản ứng emoji | ✅ | Đồng bộ hai chiều |
| `team_tasks` | Công việc nhóm | ✅ | Đồng bộ hai chiều (chỉ những công việc được phân công) |
| `drafts` | Bản nháp tin nhắn/công việc | ✅ | Đồng bộ hai chiều (chỉ của user hiện tại) |

### 2.2 Bảng chỉ lưu trên server (đồng bộ 1 chiều Server → Local)

| Bảng | Mô tả |
|------|-------|
| `users` | Thông tin người dùng |
| `user_settings` | Cài đặt người dùng |
| `teams` | Thông tin nhóm |
| `team_members` | Thành viên nhóm |
| `task_assignments` | Phân công công việc |
| `files` | Metadata tệp đính kèm |

### 2.3 Bảng chỉ lưu trên server (không đồng bộ)

| Bảng | Mô tả |
|------|-------|
| `devices` | Thiết bị đăng ký |
| `notification_queues` | Hàng đợi thông báo |
| `audit_logs` | Nhật ký hoạt động |
| `analytics_data` | Dữ liệu phân tích |
| `system_settings` | Cài đặt hệ thống |

## 3. API Endpoints

### 3.1 Xác thực

- `POST /api/auth/register`: Đăng ký tài khoản
- `POST /api/auth/login`: Đăng nhập
- `POST /api/auth/google`: Đăng nhập bằng Google
- `POST /api/auth/logout`: Đăng xuất
- `GET /api/user`: Lấy thông tin người dùng
- `POST /api/auth/set-password`: Đặt mật khẩu cho tài khoản Google
- `POST /api/auth/biometric`: Xác thực bằng sinh trắc học
- `POST /api/auth/biometric/register`: Đăng ký xác thực sinh trắc học
- `DELETE /api/auth/biometric`: Xóa xác thực sinh trắc học

### 3.2 Công việc cá nhân

- `GET /api/personal-tasks`: Danh sách công việc
- `POST /api/personal-tasks`: Tạo mới
- `GET /api/personal-tasks/{id}`: Chi tiết
- `PUT /api/personal-tasks/{id}`: Cập nhật
- `DELETE /api/personal-tasks/{id}`: Xóa
- `POST /api/personal-tasks/order`: Cập nhật thứ tự và trạng thái (Kanban)

### 3.3 Nhóm
- `GET /api/teams`: Danh sách nhóm
- `POST /api/teams`: Tạo nhóm
- `GET /api/teams/{id}`: Chi tiết nhóm
- `PUT /api/teams/{id}`: Cập nhật nhóm
- `DELETE /api/teams/{id}`: Xóa nhóm

### 3.4 Thành viên nhóm
- `GET /api/teams/{team}/members`: Danh sách thành viên
- `POST /api/teams/{team}/members`: Thêm thành viên
- `DELETE /api/teams/{team}/members/{user}`: Xóa thành viên

### 3.5 Công việc nhóm
- `GET /api/teams/{team}/tasks`: Danh sách công việc
- `POST /api/teams/{team}/tasks`: Tạo mới
- `GET /api/teams/{team}/tasks/{task}`: Chi tiết
- `PUT /api/teams/{team}/tasks/{task}`: Cập nhật
- `DELETE /api/teams/{team}/tasks/{task}`: Xóa

### 3.6 Phân công công việc
- `GET /api/teams/{team}/tasks/{task}/assignments`: Danh sách phân công
- `POST /api/teams/{team}/tasks/{task}/assignments`: Phân công mới
- `PUT /api/teams/{team}/tasks/{task}/assignments/{assignment}`: Cập nhật
- `DELETE /api/teams/{team}/tasks/{task}/assignments/{assignment}`: Hủy

### 3.7 Công việc con (Subtasks)

- `GET /api/{taskType}/{taskId}/subtasks`: Danh sách công việc con
- `POST /api/{taskType}/{taskId}/subtasks`: Tạo công việc con
- `PUT /api/{taskType}/{taskId}/subtasks/{subtask}`: Cập nhật công việc con
- `DELETE /api/{taskType}/{taskId}/subtasks/{subtask}`: Xóa công việc con
- `POST /api/{taskType}/{taskId}/subtasks/order`: Cập nhật thứ tự công việc con

### 3.8 Chat

- `GET /api/teams/{team}/chat`: Lấy lịch sử chat
- `POST /api/teams/{team}/chat`: Gửi tin nhắn
- `PUT /api/teams/{team}/chat/{message}/read`: Đánh dấu đã đọc
- `GET /api/teams/{team}/chat/unread`: Đếm tin chưa đọc
- `POST /api/teams/{team}/chat/typing`: Cập nhật trạng thái nhập
- `POST /api/teams/{team}/chat/retry/{clientTempId}`: Gửi lại tin nhắn
- `PUT /api/teams/{team}/chat/{message}`: Chỉnh sửa tin nhắn
- `DELETE /api/teams/{team}/chat/{message}`: Xóa tin nhắn
- `POST /api/teams/{team}/chat/{message}/react`: Thêm/xóa phản ứng

### 3.9 Tệp tin

- `POST /api/upload`: Tải lên tệp tin

### 3.10 Đồng bộ hóa

- `POST /api/sync/initial`: Đồng bộ lần đầu
- `POST /api/sync/quick`: Đồng bộ nhanh
- `POST /api/sync/push`: Đẩy dữ liệu local lên server

### 3.11 Thông báo

- `POST /api/devices/register`: Đăng ký thiết bị cho FCM
- `DELETE /api/devices/unregister`: Hủy đăng ký thiết bị
- `GET /api/notifications`: Lấy thông báo mới

### 3.12 Cài đặt người dùng

- `GET /api/settings`: Lấy cài đặt người dùng
- `PUT /api/settings`: Cập nhật cài đặt người dùng

### 3.13 Phân tích và báo cáo

- `GET /api/analytics/tasks`: Thống kê công việc
- `GET /api/analytics/productivity`: Điểm năng suất
- `GET /api/analytics/team-performance`: Hiệu suất nhóm

### 3.14 Lịch

- `GET /api/calendar/tasks`: Lấy công việc theo khoảng thời gian
- `GET /api/calendar/day`: Lấy công việc theo ngày
- `PUT /api/calendar/sync`: Cập nhật cài đặt đồng bộ lịch

### 3.15 Nháp tự động

- `GET /api/drafts`: Lấy danh sách nháp
- `POST /api/drafts`: Lưu nháp mới
- `GET /api/drafts/{id}`: Lấy chi tiết nháp
- `DELETE /api/drafts/{id}`: Xóa nháp

## 4. Cơ chế WebSocket

### 4.1 Kết nối

```text
wss://your-api.com/reverb?token={access_token}
```

### 4.2 Kênh

- `private-teams.{teamId}`: Kênh riêng cho mỗi nhóm

### 4.3 Events

- `new-chat-message`: Tin nhắn mới
- `message-read`: Tin nhắn đã đọc
- `user-typing`: Người dùng đang nhập
- `message-reaction-updated`: Cập nhật phản ứng
- `message-updated`: Tin nhắn được chỉnh sửa
- `message-deleted`: Tin nhắn bị xóa

## 5. Hướng dẫn triển khai

### 5.1 Yêu cầu hệ thống

- PHP 8.2 hoặc cao hơn
- MySQL 8.0 hoặc cao hơn
- Composer
- Node.js và npm (cho frontend)

### 5.2 Cài đặt

1. **Clone repository**:
   ```bash
   git clone https://github.com/your-username/task-management-api.git
   cd task-management-api
   ```

2. **Cài đặt dependencies**:
   ```bash
   composer install
   ```

3. **Cấu hình môi trường**:
   - Sao chép file `.env.example` thành `.env`
   - Cấu hình kết nối database
   - Cấu hình Reverb WebSocket
   - Cấu hình Firebase (FCM)
   - Cấu hình Google OAuth (nếu sử dụng)

4. **Tạo key ứng dụng**:
   ```bash
   php artisan key:generate
   ```

5. **Chạy migrations**:
   ```bash
   php artisan migrate
   ```

6. **Chạy server**:
   ```bash
   php artisan serve
   ```

7. **Chạy WebSocket server**:
   ```bash
   php artisan reverb:start
   ```

### 5.3 Triển khai Android App

1. **Cài đặt Android Studio** và cấu hình dự án
2. **Cấu hình API endpoints** trong app/build.gradle
3. **Cấu hình WebSocket** cho tính năng realtime
4. **Cấu hình Firebase** cho push notification
5. **Triển khai UI/UX** với Jetpack Compose và Material 3

### 5.4 Tính năng mới đã triển khai

1. **Công việc con (Subtasks)**: Hỗ trợ tạo và quản lý công việc con cho cả công việc cá nhân và nhóm
2. **Cài đặt người dùng**: Hỗ trợ tùy chỉnh theme, ngôn ngữ, thông báo
3. **Phân tích và báo cáo**: Cung cấp thống kê công việc, điểm năng suất, hiệu suất nhóm
4. **Lịch**: Hỗ trợ xem công việc theo lịch và đồng bộ với lịch bên ngoài
5. **Kanban**: Hỗ trợ kéo thả và sắp xếp công việc
6. **Xác thực sinh trắc học**: Hỗ trợ đăng nhập bằng vân tay/khuôn mặt
7. **Tự động lưu nháp**: Lưu nội dung khi người dùng thoát giữa chừng

## 6. Xử lý vấn đề

### 6.1 Xử lý lỗi phổ biến

- **Lỗi kết nối database**: Kiểm tra cấu hình trong file `.env`
- **Lỗi WebSocket**: Đảm bảo Reverb server đang chạy
- **Lỗi CORS**: Cấu hình CORS trong `config/cors.php`

### 6.2 Hỗ trợ

Nếu bạn gặp vấn đề trong quá trình triển khai, vui lòng liên hệ:
- Email: <EMAIL>
- Tài liệu API: https://api-docs.example.com

## 6. Xử lý vấn đề

### 6.1 Xử lý lỗi phổ biến

- **Lỗi kết nối database**: Kiểm tra cấu hình trong file `.env`
- **Lỗi WebSocket**: Đảm bảo Reverb server đang chạy
- **Lỗi CORS**: Cấu hình CORS trong `config/cors.php`

### 6.2 Hỗ trợ

Nếu bạn gặp vấn đề trong quá trình triển khai, vui lòng liên hệ:
- Email: <EMAIL>
- Tài liệu API: [https://api-docs.example.com](https://api-docs.example.com)


## 6. Xử lý offline

### 6.1 Cơ chế hoạt động

- **Local-first**: Lưu trữ và xử lý dữ liệu trên thiết bị trước
- **Optimistic updates**: Cập nhật UI ngay không chờ server
- **Background sync**: Đồng bộ khi có kết nối
- **Conflict resolution**: Server-wins để giải quyết xung đột

### 6.2 Flow xử lý offline

1. **Tạo item mới**:
   - Lưu vào DB local với unique ID tạm thời
   - Đánh dấu item là "chưa đồng bộ"
   - Cập nhật UI ngay lập tức
   - Đưa vào queue để đồng bộ khi có mạng

2. **Cập nhật item**:
   - Cập nhật trong DB local
   - Đánh dấu item là "đã chỉnh sửa, chưa đồng bộ"
   - Đưa vào queue để đồng bộ

3. **Đồng bộ khi có mạng**:
   - Đẩy tất cả item chưa đồng bộ lên server
   - Cập nhật ID tạm thời thành ID thật từ server
   - Đánh dấu item là "đã đồng bộ"

## 7. Xử lý vấn đề khác

### 7.1 Bảo mật

- Sử dụng HTTPS cho mọi request
- Token-based authentication (Laravel Sanctum)
- Lưu token trong EncryptedSharedPreferences
- Auto-refresh token khi cần

### 7.2 Tối ưu hiệu suất

- Pagination cho danh sách và tin nhắn
- Lazy loading hình ảnh và tệp đính kèm
- Cache dữ liệu phù hợp

### 7.3 Xử lý lỗi

- Retry strategy cho API calls
- Exponential backoff cho kết nối WebSocket
- Offline fallback khi không có mạng

## 8. Deployment và thử nghiệm

- Sản xuất: [https://api-example.com](https://api-example.com)
- Staging: [https://staging-api-example.com](https://staging-api-example.com)

## 9. Liên hệ hỗ trợ

- Admin: [<EMAIL>](mailto:<EMAIL>)
- Developer: [<EMAIL>](mailto:<EMAIL>)