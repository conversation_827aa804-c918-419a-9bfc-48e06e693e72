# API Quản lý Công việc và Chat Nhóm

API này được xây dựng trên nền tả<PERSON>, cung cấp các endpoint và chiến lược cho ứng dụng có khả năng:

- Qu<PERSON>n lý công việc cá nhân và nhóm
- Chat nhóm realtime
- Chia sẻ và quản lý tài liệu
- Đồng bộ dữ liệu giữa thiết bị và server
- Hỗ trợ hoạt động offline
- Thông báo đẩy
- Phân tích và báo cáo
- T<PERSON>ch hợp lịch
- Giao diện Kanban
- Quản lý công việc con (subtasks)
- Tùy chỉnh giao diện (theme, ngôn ngữ)
- <PERSON><PERSON><PERSON> thự<PERSON> sinh trắc học (Fingerprint/Face Unlock)
- Tự động lưu nháp khi thoát giữa chừng

## 1. Kiến trúc tổng quan

### 1.1 Stack công nghệ

- **Backend**: <PERSON><PERSON>, MySQL
- **WebSocket**: <PERSON><PERSON> Reverb
- **API Authentication**: Sanctum Token-based

### 1.2 Luồng dữ liệu

- **Realtime**: WebSocket cho chat, typing indicator, read status
- **Đồng bộ**: RESTful API để đồng bộ dữ liệu
- **Thông báo**: Firebase Cloud Messaging (FCM) cho push notification

## 2. Cấu trúc dữ liệu

### 2.1 Bảng cần đồng bộ (local và server)

| Bảng | Mô tả | Đồng bộ | Chiến lược |
|------|-------|---------|------------|
| `personal_tasks` | Công việc cá nhân | ✅ | Đồng bộ hai chiều |
| `subtasks` | Công việc con | ✅ | Đồng bộ hai chiều |
| `messages` | Tin nhắn nhóm | ✅ | Push lên server, WebSocket xuống thiết bị |
| `message_read_status` | Trạng thái đọc | ✅ | Đồng bộ hai chiều |
| `message_reactions` | Phản ứng emoji | ✅ | Đồng bộ hai chiều |
| `team_tasks` | Công việc nhóm | ✅ | Đồng bộ hai chiều (chỉ những công việc được phân công) |
| `documents` | Tài liệu | ✅ | Đồng bộ hai chiều |
| `document_versions` | Phiên bản tài liệu | ✅ | Đồng bộ hai chiều |
| `drafts` | Bản nháp tin nhắn/công việc | ✅ | Đồng bộ hai chiều (chỉ của user hiện tại) |

### 2.2 Bảng chỉ lưu trên server (đồng bộ 1 chiều Server → Local)

| Bảng | Mô tả |
|------|-------|
| `users` | Thông tin người dùng |
| `user_settings` | Cài đặt người dùng |
| `teams` | Thông tin nhóm |
| `team_members` | Thành viên nhóm |
| `team_role_history` | Lịch sử thay đổi vai trò thành viên |
| `task_assignments` | Phân công công việc |
| `files` | Metadata tệp đính kèm |
| `document_folders` | Thư mục tài liệu |
| `document_user_permissions` | Quyền truy cập tài liệu |

### 2.3 Bảng chỉ lưu trên server (không đồng bộ)

| Bảng | Mô tả |
|------|-------|
| `devices` | Thiết bị đăng ký |
| `notification_queues` | Hàng đợi thông báo |
| `audit_logs` | Nhật ký hoạt động |
| `analytics_data` | Dữ liệu phân tích |
| `system_settings` | Cài đặt hệ thống |

## 3. API Endpoints

### 3.1 Xác thực

- `POST /api/auth/register`: Đăng ký tài khoản
- `POST /api/auth/login`: Đăng nhập
- `POST /api/auth/google`: Đăng nhập bằng Google
- `POST /api/auth/logout`: Đăng xuất
- `GET /api/user`: Lấy thông tin người dùng
- `POST /api/auth/set-password`: Đặt mật khẩu cho tài khoản Google
- `POST /api/auth/biometric`: Xác thực bằng sinh trắc học
- `POST /api/auth/biometric/register`: Đăng ký xác thực sinh trắc học
- `DELETE /api/auth/biometric`: Xóa xác thực sinh trắc học
- `POST /api/auth/change-password`: Đổi mật khẩu
- `POST /api/auth/forgot-password`: Quên mật khẩu
- `POST /api/auth/reset-password`: Đặt lại mật khẩu
- `POST /api/auth/2fa/setup`: Thiết lập xác thực hai yếu tố
- `POST /api/auth/2fa/verify`: Xác minh mã xác thực hai yếu tố

### 3.2 Công việc cá nhân

- `GET /api/personal-tasks`: Danh sách công việc
- `POST /api/personal-tasks`: Tạo mới
- `GET /api/personal-tasks/{id}`: Chi tiết
- `PUT /api/personal-tasks/{id}`: Cập nhật
- `DELETE /api/personal-tasks/{id}`: Xóa
- `POST /api/personal-tasks/order`: Cập nhật thứ tự và trạng thái (Kanban)
- `GET /api/personal-tasks/filter`: Lọc công việc theo trạng thái, ưu tiên, deadline
- `GET /api/personal-tasks/search`: Tìm kiếm công việc theo từ khóa

### 3.3 Nhóm

- `GET /api/teams`: Danh sách nhóm
- `POST /api/teams`: Tạo nhóm
- `GET /api/teams/{id}`: Chi tiết nhóm
- `PUT /api/teams/{id}`: Cập nhật nhóm
- `DELETE /api/teams/{id}`: Xóa nhóm

### 3.4 Thành viên nhóm

- `GET /api/teams/{team}/members`: Danh sách thành viên
- `POST /api/teams/{team}/members`: Thêm thành viên
- `DELETE /api/teams/{team}/members/{user}`: Xóa thành viên
- `PUT /api/teams/{team}/members/{user}/role`: Cập nhật vai trò thành viên
- `PUT /api/teams/{team}/members/{user}/permissions`: Cập nhật quyền hạn thành viên

### 3.5 Lời mời nhóm

- `GET /api/teams/{team}/invitations`: Lấy danh sách lời mời
- `POST /api/teams/{team}/invitations`: Gửi lời mời tham gia nhóm
- `POST /api/invitations/accept`: Chấp nhận lời mời
- `POST /api/invitations/reject`: Từ chối lời mời
- `DELETE /api/teams/{team}/invitations/{invitation}`: Xóa lời mời

### 3.6 Công việc nhóm

- `GET /api/teams/{team}/tasks`: Danh sách công việc
- `POST /api/teams/{team}/tasks`: Tạo mới
- `GET /api/teams/{team}/tasks/{task}`: Chi tiết
- `PUT /api/teams/{team}/tasks/{task}`: Cập nhật
- `DELETE /api/teams/{team}/tasks/{task}`: Xóa
- `GET /api/teams/{team}/tasks/filter`: Lọc công việc nhóm theo trạng thái, ưu tiên, deadline, người được giao
- `GET /api/teams/{team}/tasks/search`: Tìm kiếm công việc nhóm theo từ khóa

### 3.7 Phân công công việc

- `GET /api/teams/{team}/tasks/{task}/assignments`: Danh sách phân công
- `POST /api/teams/{team}/tasks/{task}/assignments`: Phân công mới
- `PUT /api/teams/{team}/tasks/{task}/assignments/{assignment}`: Cập nhật
- `DELETE /api/teams/{team}/tasks/{task}/assignments/{assignment}`: Hủy

### 3.8 Công việc con (Subtasks)

- `GET /api/{taskType}/{taskId}/subtasks`: Danh sách công việc con
- `POST /api/{taskType}/{taskId}/subtasks`: Tạo công việc con
- `PUT /api/{taskType}/{taskId}/subtasks/{subtask}`: Cập nhật công việc con
- `DELETE /api/{taskType}/{taskId}/subtasks/{subtask}`: Xóa công việc con
- `POST /api/{taskType}/{taskId}/subtasks/order`: Cập nhật thứ tự công việc con

### 3.9 Chat

- `GET /api/teams/{team}/chat`: Lấy lịch sử chat
- `POST /api/teams/{team}/chat`: Gửi tin nhắn
- `PUT /api/teams/{team}/chat/{message}/read`: Đánh dấu đã đọc
- `GET /api/teams/{team}/chat/unread`: Đếm tin chưa đọc
- `POST /api/teams/{team}/chat/typing`: Cập nhật trạng thái nhập
- `POST /api/teams/{team}/chat/retry/{clientTempId}`: Gửi lại tin nhắn
- `PUT /api/teams/{team}/chat/{message}`: Chỉnh sửa tin nhắn
- `DELETE /api/teams/{team}/chat/{message}`: Xóa tin nhắn
- `POST /api/teams/{team}/chat/{message}/react`: Thêm/xóa phản ứng

### 3.10 Tệp tin và Tài liệu

- `POST /api/upload`: Tải lên tệp tin

### 3.11 Quản lý Tài liệu

- `GET /api/teams/{team}/documents`: Lấy danh sách tài liệu trong nhóm
- `POST /api/teams/{team}/documents`: Tải lên tài liệu mới
- `GET /api/documents/{document}`: Lấy chi tiết tài liệu
- `PUT /api/documents/{document}`: Cập nhật thông tin tài liệu
- `DELETE /api/documents/{document}`: Xóa tài liệu
- `GET /api/documents/{document}/download`: Tải xuống tài liệu
- `PUT /api/documents/{document}/access`: Cập nhật quyền truy cập tài liệu

### 3.12 Quản lý Thư mục Tài liệu

- `GET /api/teams/{team}/folders`: Lấy danh sách thư mục
- `POST /api/teams/{team}/folders`: Tạo thư mục mới
- `GET /api/folders/{folder}`: Lấy chi tiết thư mục
- `PUT /api/folders/{folder}`: Cập nhật thư mục
- `DELETE /api/folders/{folder}`: Xóa thư mục

### 3.13 Quản lý Phiên bản Tài liệu

- `GET /api/documents/{document}/versions`: Lấy danh sách phiên bản
- `POST /api/documents/{document}/versions`: Tải lên phiên bản mới
- `GET /api/documents/{document}/versions/{versionNumber}`: Lấy chi tiết phiên bản
- `GET /api/documents/{document}/versions/{versionNumber}/download`: Tải xuống phiên bản cụ thể
- `POST /api/documents/{document}/versions/{versionNumber}/restore`: Khôi phục phiên bản cũ

### 3.14 Đồng bộ hóa

- `POST /api/sync/initial`: Đồng bộ lần đầu
- `POST /api/sync/quick`: Đồng bộ nhanh
- `POST /api/sync/push`: Đẩy dữ liệu local lên server
- `POST /api/sync/selective`: Đồng bộ có chọn lọc theo loại dữ liệu
- `POST /api/sync/resolve-conflicts`: Xử lý xung đột giữa dữ liệu local và server

### 3.15 Thông báo

- `POST /api/devices/register`: Đăng ký thiết bị cho FCM
- `DELETE /api/devices/unregister`: Hủy đăng ký thiết bị
- `GET /api/notifications`: Lấy thông báo mới
- `PUT /api/notifications/settings`: Cập nhật cài đặt thông báo
- `POST /api/notifications/mark-read`: Đánh dấu thông báo đã đọc

### 3.16 Cài đặt người dùng

- `GET /api/settings`: Lấy cài đặt người dùng
- `PUT /api/settings`: Cập nhật cài đặt người dùng
- `POST /api/settings/reset`: Đặt lại cài đặt về mặc định

### 3.17 Phân tích và báo cáo

- `GET /api/analytics/tasks`: Thống kê công việc
- `GET /api/analytics/productivity`: Điểm năng suất
- `GET /api/analytics/team-performance`: Hiệu suất nhóm
- `GET /api/analytics/export`: Xuất báo cáo sang định dạng PDF hoặc CSV

### 3.18 Lịch

- `GET /api/calendar/tasks`: Lấy công việc theo khoảng thời gian
- `GET /api/calendar/day`: Lấy công việc theo ngày
- `PUT /api/calendar/sync`: Cập nhật cài đặt đồng bộ lịch
- `GET /api/calendar/export`: Xuất lịch sang định dạng iCal hoặc CSV

### 3.18 Nháp tự động

- `GET /api/drafts`: Lấy danh sách nháp
- `POST /api/drafts`: Lưu nháp mới
- `GET /api/drafts/{id}`: Lấy chi tiết nháp
- `DELETE /api/drafts/{id}`: Xóa nháp

### 3.19 Kanban

- `GET /api/teams/{team}/kanban`: Lấy dữ liệu bảng Kanban cho nhóm
- `PUT /api/teams/{team}/kanban/tasks/{task}/move`: Di chuyển công việc giữa các cột
- `PUT /api/teams/{team}/kanban/column-order`: Cập nhật thứ tự công việc trong một cột

## 4. Cơ chế WebSocket

### 4.1 Kết nối

```text
wss://your-api.com/reverb?token={access_token}
```

### 4.2 Kênh

- `private-teams.{teamId}`: Kênh riêng cho mỗi nhóm

### 4.3 Events

- `new-chat-message`: Tin nhắn mới
- `message-read`: Tin nhắn đã đọc
- `user-typing`: Người dùng đang nhập
- `message-reaction-updated`: Cập nhật phản ứng
- `message-updated`: Tin nhắn được chỉnh sửa
- `message-deleted`: Tin nhắn bị xóa

## 5. Hướng dẫn triển khai

### 5.1 Yêu cầu hệ thống

- PHP 8.2 hoặc cao hơn
- MySQL 8.0 hoặc cao hơn
- Composer
- Node.js và npm (cho frontend)

### 5.2 Cài đặt

1. **Clone repository**:

   ```bash
   git clone https://github.com/your-username/task-management-api.git
   cd task-management-api
   ```

2. **Cài đặt dependencies**:

   ```bash
   composer install
   ```

3. **Cấu hình môi trường**:
   - Sao chép file `.env.example` thành `.env`
   - Cấu hình kết nối database
   - Cấu hình Reverb WebSocket
   - Cấu hình Firebase (FCM)
   - Cấu hình Google OAuth (nếu sử dụng)

4. **Tạo key ứng dụng**:

   ```bash
   php artisan key:generate
   ```

5. **Chạy migrations**:

   ```bash
   php artisan migrate
   ```

6. **Chạy server**:

   ```bash
   php artisan serve
   ```

7. **Chạy WebSocket server**:

   ```bash
   php artisan reverb:start
   ```

### 5.3 Triển khai Android App

1. **Cài đặt Android Studio** và cấu hình dự án
2. **Cấu hình API endpoints** trong app/build.gradle
3. **Cấu hình WebSocket** cho tính năng realtime
4. **Cấu hình Firebase** cho push notification
5. **Triển khai UI/UX** với Jetpack Compose và Material 3

### 5.4 Tính năng mới đã triển khai (Cập nhật)

1. **Công việc con (Subtasks)**: Hỗ trợ tạo và quản lý công việc con cho cả công việc cá nhân và nhóm
2. **Chia sẻ tài liệu**: Hỗ trợ tải lên, quản lý và chia sẻ tài liệu trong nhóm
3. **Quản lý phiên bản tài liệu**: Hỗ trợ lưu trữ và khôi phục các phiên bản tài liệu
4. **Cài đặt người dùng**: Hỗ trợ tùy chỉnh theme, ngôn ngữ, thông báo
5. **Phân tích và báo cáo**: Cung cấp thống kê công việc, điểm năng suất, hiệu suất nhóm
6. **Lịch**: Hỗ trợ xem công việc theo lịch và đồng bộ với lịch bên ngoài
7. **Kanban**: Hỗ trợ kéo thả và sắp xếp công việc
8. **Xác thực sinh trắc học**: Hỗ trợ đăng nhập bằng vân tay/khuôn mặt
9. **Tự động lưu nháp**: Lưu nội dung khi người dùng thoát giữa chừng
10. **Đổi mật khẩu và quên mật khẩu**: Hỗ trợ đổi mật khẩu và khôi phục mật khẩu qua email
11. **Xác thực hai yếu tố**: Bảo mật tài khoản với xác thực hai yếu tố
12. **Lọc và tìm kiếm công việc**: Tìm kiếm và lọc công việc theo nhiều tiêu chí
13. **Quản lý vai trò và phân quyền**: Phân quyền chi tiết cho thành viên nhóm
14. **Lịch sử vai trò thành viên**: Theo dõi thay đổi vai trò của thành viên trong nhóm
15. **Đồng bộ có chọn lọc và xử lý xung đột**: Tối ưu hóa đồng bộ dữ liệu
16. **Xuất lịch và báo cáo**: Xuất dữ liệu sang các định dạng phổ biến

## 6. Xử lý vấn đề

### 6.1 Xử lý lỗi phổ biến

- **Lỗi kết nối database**: Kiểm tra cấu hình trong file `.env`
- **Lỗi WebSocket**: Đảm bảo Reverb server đang chạy
- **Lỗi CORS**: Cấu hình CORS trong `config/cors.php`

### 6.2 Hỗ trợ

Nếu bạn gặp vấn đề trong quá trình triển khai, vui lòng liên hệ:

- Email: [<EMAIL>](mailto:<EMAIL>)
- Tài liệu API: [api-docs.example.com](https://api-docs.example.com)

## 7. Xử lý offline

### 7.1 Cơ chế hoạt động

- **Local-first**: Lưu trữ và xử lý dữ liệu trên thiết bị trước
- **Optimistic updates**: Cập nhật UI ngay không chờ server
- **Background sync**: Đồng bộ khi có kết nối
- **Conflict resolution**: Server-wins để giải quyết xung đột

### 7.2 Flow xử lý offline

1. **Tạo item mới**:
   - Lưu vào DB local với unique ID tạm thời
   - Đánh dấu item là "chưa đồng bộ"
   - Cập nhật UI ngay lập tức
   - Đưa vào queue để đồng bộ khi có mạng

2. **Cập nhật item**:
   - Cập nhật trong DB local
   - Đánh dấu item là "đã chỉnh sửa, chưa đồng bộ"
   - Đưa vào queue để đồng bộ

3. **Đồng bộ khi có mạng**:
   - Đẩy tất cả item chưa đồng bộ lên server
   - Cập nhật ID tạm thời thành ID thật từ server
   - Đánh dấu item là "đã đồng bộ"

## 8. Xử lý vấn đề khác

### 8.1 Bảo mật

- Sử dụng HTTPS cho mọi request
- Token-based authentication (Laravel Sanctum)
- Lưu token trong EncryptedSharedPreferences
- Auto-refresh token khi cần

### 8.2 Tối ưu hiệu suất

- Pagination cho danh sách và tin nhắn
- Lazy loading hình ảnh và tệp đính kèm
- Cache dữ liệu phù hợp

### 8.3 Xử lý lỗi

- Retry strategy cho API calls
- Exponential backoff cho kết nối WebSocket
- Offline fallback khi không có mạng


