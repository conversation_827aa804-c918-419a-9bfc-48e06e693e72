# 📱 Task Management API

## 🎯 Giới thiệu

**Task Management API** là một hệ thống backend hoàn chỉnh được xây dựng bằng Laravel Framework để phục vụ ứng dụng mobile quản lý công việc và chat nhóm. API hỗ trợ cả hoạt động offline và đồng bộ real-time.

## 🚀 Quick Start

### ⚙️ Cài đặt nhanh
```bash
# Clone và setup
git clone <repository-url>
cd API_TaskMobileApp
composer install

# Database setup
cp .env.example .env
php artisan key:generate
php artisan migrate
php artisan db:seed --class=AdminUserSeeder

# Start servers
php artisan serve --host=0.0.0.0 --port=8000
php artisan reverb:start --host=0.0.0.0 --port=8080
```

### 🌐 URLs
- **API Server**: `http://localhost:8000/api/`
- **Admin Panel**: `http://localhost:8000/admin/`
- **WebSocket**: `ws://localhost:8080/ws`

### 👤 Default Admin
```
Email: <EMAIL>
Password: admin123
```

## 📚 Tài liệu đầy đủ

### 📖 Tài liệu chính
- **[PROJECT_DOCUMENTATION.md](PROJECT_DOCUMENTATION.md)** - 📋 Tài liệu tổng hợp đầy đủ
- **[API_DOCUMENTATION.md](API_DOCUMENTATION.md)** - 🌐 Chi tiết tất cả API endpoints
- **[ADMIN_PANEL_SETUP.md](ADMIN_PANEL_SETUP.md)** - 🛡️ Hướng dẫn admin panel

### 🔗 Database Schema
- **[dbdiagram_code.txt](dbdiagram_code.txt)** - 🗄️ ERD database schema

## ✨ Tính năng nổi bật

### 🔐 Authentication & Security
- JWT Token Authentication
- Google OAuth Sign-In
- Biometric Authentication
- Two-Factor Authentication (2FA)
- Multi-device Support

### 📝 Task Management
- **Personal Tasks**: CRUD, categories, priorities, deadlines
- **Team Tasks**: Collaborative task management, assignments
- **Subtasks**: Hierarchical task organization
- **Kanban Board**: Visual task management

### 👥 Team Collaboration
- Team creation & management
- Role-based permissions (Admin/Member)
- Team invitations system
- Real-time team chat with WebSocket
- Document sharing & versioning

### 🔄 Offline & Sync
- **Offline-first**: App hoạt động khi không có mạng
- **5 loại sync**: Initial, Quick, Push, Selective, Conflict Resolution
- **Auto-save drafts**: Tự động lưu khi đang nhập
- **Multi-device sync**: Đồng bộ giữa nhiều thiết bị

### 🛡️ Admin Panel
- System dashboard với thống kê
- User & team management
- Privacy-protected (không xem nội dung riêng tư)
- Responsive design

## 🛠️ Tech Stack

```
Backend:     Laravel 12.0, PHP 8.2+
Database:    MySQL 8.0+
WebSocket:   Laravel Reverb
Auth:        Laravel Sanctum + JWT
Push:        Firebase Cloud Messaging
OAuth:       Google Sign-In
Admin UI:    Bootstrap 5 + Blade
```

## 📱 Android Integration

### 🔌 API Connection
```kotlin
// Base URLs
API_BASE_URL = "http://10.0.2.2:8000/api/"
WEBSOCKET_URL = "ws://10.0.2.2:8080/ws"

// Authentication
Authorization: Bearer {token}
```

### 🔄 Sync Strategy
```kotlin
// 5 types of sync
1. Initial Sync    - First time data load
2. Quick Sync      - Delta changes (every 15 min)
3. Push Sync       - Upload offline changes
4. Selective Sync  - User-chosen data types
5. Conflict Sync   - Resolve data conflicts
```

### 💾 Local Storage
```sql
-- SQLite tables needed
users, personal_tasks, teams, team_tasks
chat_messages, documents, sync_status
pending_actions, drafts
```

## 🌐 API Overview

### 📍 Main Endpoints (217 total routes)
```
Authentication (11):  /api/auth/*
Personal Tasks (7):   /api/personal-tasks/*
Teams (5):           /api/teams/*
Team Chat (8):       /api/teams/{team}/chat/*
Documents (10):      /api/documents/*
Sync (5):           /api/sync/*
Admin Panel:        /admin/*
```

### 🔄 Real-time Events
```javascript
// WebSocket events
new-chat-message
message-read
user-typing
team-invitation-created
message-reaction-updated
```

## 🗄️ Database

### 📊 30+ Tables
```sql
-- Core entities
users, teams, personal_tasks, team_tasks

-- Communication
group_chat_messages, message_reactions

-- Documents
documents, document_folders, document_versions

-- Sync & Offline
sync_status, drafts, devices

-- Admin & System
sessions, cache, jobs, migrations
```

## 4. Cơ chế WebSocket

### 4.1 Kết nối

```text
wss://your-api.com/reverb?token={access_token}
```

### 4.2 Kênh

- `private-teams.{teamId}`: Kênh riêng cho mỗi nhóm

### 4.3 Events

- `new-chat-message`: Tin nhắn mới
- `message-read`: Tin nhắn đã đọc
- `user-typing`: Người dùng đang nhập
- `message-reaction-updated`: Cập nhật phản ứng
- `message-updated`: Tin nhắn được chỉnh sửa
- `message-deleted`: Tin nhắn bị xóa

## 5. Hướng dẫn triển khai

### 5.1 Yêu cầu hệ thống

- PHP 8.2 hoặc cao hơn
- MySQL 8.0 hoặc cao hơn
- Composer
- Node.js và npm (cho frontend)

### 5.2 Cài đặt

1. **Clone repository**:

   ```bash
   git clone https://github.com/your-username/task-management-api.git
   cd task-management-api
   ```

2. **Cài đặt dependencies**:

   ```bash
   composer install
   ```

3. **Cấu hình môi trường**:
   - Sao chép file `.env.example` thành `.env`
   - Cấu hình kết nối database
   - Cấu hình Reverb WebSocket
   - Cấu hình Firebase (FCM)
   - Cấu hình Google OAuth (nếu sử dụng)

4. **Tạo key ứng dụng**:

   ```bash
   php artisan key:generate
   ```

5. **Chạy migrations**:

   ```bash
   php artisan migrate
   ```

6. **Chạy server**:

   ```bash
   php artisan serve
   ```

7. **Chạy WebSocket server**:

   ```bash
   php artisan reverb:start
   ```

### 5.3 Triển khai Android App

1. **Cài đặt Android Studio** và cấu hình dự án
2. **Cấu hình API endpoints** trong app/build.gradle
3. **Cấu hình WebSocket** cho tính năng realtime
4. **Cấu hình Firebase** cho push notification
5. **Triển khai UI/UX** với Jetpack Compose và Material 3

### 5.4 Tính năng mới đã triển khai (Cập nhật)

1. **Công việc con (Subtasks)**: Hỗ trợ tạo và quản lý công việc con cho cả công việc cá nhân và nhóm
2. **Chia sẻ tài liệu**: Hỗ trợ tải lên, quản lý và chia sẻ tài liệu trong nhóm
3. **Quản lý phiên bản tài liệu**: Hỗ trợ lưu trữ và khôi phục các phiên bản tài liệu
4. **Cài đặt người dùng**: Hỗ trợ tùy chỉnh theme, ngôn ngữ, thông báo
5. **Phân tích và báo cáo**: Cung cấp thống kê công việc, điểm năng suất, hiệu suất nhóm
6. **Lịch**: Hỗ trợ xem công việc theo lịch và đồng bộ với lịch bên ngoài
7. **Kanban**: Hỗ trợ kéo thả và sắp xếp công việc
8. **Xác thực sinh trắc học**: Hỗ trợ đăng nhập bằng vân tay/khuôn mặt
9. **Tự động lưu nháp**: Lưu nội dung khi người dùng thoát giữa chừng
10. **Đổi mật khẩu và quên mật khẩu**: Hỗ trợ đổi mật khẩu và khôi phục mật khẩu qua email
11. **Xác thực hai yếu tố**: Bảo mật tài khoản với xác thực hai yếu tố
12. **Lọc và tìm kiếm công việc**: Tìm kiếm và lọc công việc theo nhiều tiêu chí
13. **Quản lý vai trò và phân quyền**: Phân quyền chi tiết cho thành viên nhóm
14. **Lịch sử vai trò thành viên**: Theo dõi thay đổi vai trò của thành viên trong nhóm
15. **Đồng bộ có chọn lọc và xử lý xung đột**: Tối ưu hóa đồng bộ dữ liệu
16. **Xuất lịch và báo cáo**: Xuất dữ liệu sang các định dạng phổ biến
17. **Lời mời nhóm**: Hỗ trợ gửi, chấp nhận, từ chối lời mời tham gia nhóm

## 6. Xử lý vấn đề

### 6.1 Xử lý lỗi phổ biến

- **Lỗi kết nối database**: Kiểm tra cấu hình trong file `.env`
- **Lỗi WebSocket**: Đảm bảo Reverb server đang chạy
- **Lỗi CORS**: Cấu hình CORS trong `config/cors.php`

### 6.2 Hỗ trợ

Nếu bạn gặp vấn đề trong quá trình triển khai, vui lòng liên hệ:

- Email: [<EMAIL>](mailto:<EMAIL>)
- Tài liệu API: [api-docs.example.com](https://api-docs.example.com)

## 7. Xử lý offline

### 7.1 Cơ chế hoạt động

- **Local-first**: Lưu trữ và xử lý dữ liệu trên thiết bị trước
- **Optimistic updates**: Cập nhật UI ngay không chờ server
- **Background sync**: Đồng bộ khi có kết nối
- **Conflict resolution**: Server-wins để giải quyết xung đột

### 7.2 Flow xử lý offline

1. **Tạo item mới**:
   - Lưu vào DB local với unique ID tạm thời
   - Đánh dấu item là "chưa đồng bộ"
   - Cập nhật UI ngay lập tức
   - Đưa vào queue để đồng bộ khi có mạng

2. **Cập nhật item**:
   - Cập nhật trong DB local
   - Đánh dấu item là "đã chỉnh sửa, chưa đồng bộ"
   - Đưa vào queue để đồng bộ

3. **Đồng bộ khi có mạng**:
   - Đẩy tất cả item chưa đồng bộ lên server
   - Cập nhật ID tạm thời thành ID thật từ server
   - Đánh dấu item là "đã đồng bộ"

## 8. Xử lý vấn đề khác

### 8.1 Bảo mật

- Sử dụng HTTPS cho mọi request
- Token-based authentication (Laravel Sanctum)
- Lưu token trong EncryptedSharedPreferences
- Auto-refresh token khi cần

### 8.2 Tối ưu hiệu suất

- Pagination cho danh sách và tin nhắn
- Lazy loading hình ảnh và tệp đính kèm
- Cache dữ liệu phù hợp

### 8.3 Xử lý lỗi

- Retry strategy cho API calls
- Exponential backoff cho kết nối WebSocket
- Offline fallback khi không có mạng
