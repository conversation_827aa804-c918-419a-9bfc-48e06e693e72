<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MessageReaction extends Model
{
    use HasFactory;
    
    protected $fillable = [
        'message_id',
        'user_id',
        'reaction'
    ];
    
    public function message()
    {
        return $this->belongsTo(GroupChatMessage::class, 'message_id');
    }
    
    public function user()
    {
        return $this->belongsTo(User::class);
    }
} 