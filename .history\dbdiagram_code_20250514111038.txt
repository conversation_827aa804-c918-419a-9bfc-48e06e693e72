// Task Management Mobile App Database Schema

// Users and Authentication
Table users {
  id bigint [pk, increment]
  name varchar
  email varchar [unique]
  password varchar [null]
  phone varchar [null]
  email_verified_at timestamp [null]
  google_id varchar [null, unique]
  avatar varchar [null]
  remember_token varchar [null]
  created_at timestamp
  updated_at timestamp
}

Table biometric_auth {
  id bigint [pk, increment]
  user_id bigint [ref: > users.id]
  device_id varchar
  biometric_token varchar
  last_used_at timestamp
  created_at timestamp
  updated_at timestamp
}

// Personal Tasks
Table personal_tasks {
  id bigint [pk, increment]
  user_id bigint [ref: > users.id]
  title varchar
  description text [null]
  deadline datetime [null]
  priority int [null]
  status enum('pending','in_progress','completed','overdue') [default: 'pending']
  order int [default: 0]
  created_at timestamp
  updated_at timestamp
}

// Teams
Table teams {
  id bigint [pk, increment]
  name varchar
  description text [null]
  created_by bigint [ref: > users.id]
  created_at timestamp
  updated_at timestamp
}

Table team_members {
  id bigint [pk, increment]
  team_id bigint [ref: > teams.id]
  user_id bigint [ref: > users.id]
  role enum('manager','member') [default: 'member']
  joined_at timestamp
  created_at timestamp
  updated_at timestamp
}

Table team_role_history {
  id bigint [pk, increment]
  team_id bigint [ref: > teams.id]
  user_id bigint [ref: > users.id]
  changed_by bigint [ref: > users.id]
  old_role varchar
  new_role varchar
  reason text [null]
  created_at timestamp
  updated_at timestamp
}

Table team_invitations {
  id bigint [pk, increment]
  team_id bigint [ref: > teams.id]
  created_by bigint [ref: > users.id]
  email varchar
  role enum('manager','member') [default: 'member']
  token varchar(64) [unique]
  status enum('pending','accepted','rejected','expired') [default: 'pending']
  expires_at timestamp
  created_at timestamp
  updated_at timestamp

  indexes {
    (team_id, email) [unique]
  }
}

// Team Tasks
Table team_tasks {
  id bigint [pk, increment]
  team_id bigint [ref: > teams.id]
  created_by bigint [ref: > users.id]
  title varchar
  description text [null]
  deadline datetime [null]
  priority int [null]
  status enum('pending','in_progress','completed','overdue') [default: 'pending']
  order int [default: 0]
  column_id bigint [ref: > kanban_columns.id, null]
  created_at timestamp
  updated_at timestamp
}

Table team_task_assignments {
  id bigint [pk, increment]
  team_task_id bigint [ref: > team_tasks.id]
  assigned_to bigint [ref: > users.id]
  status enum('pending','in_progress','completed') [default: 'pending']
  progress int [default: 0]
  assigned_at timestamp
  created_at timestamp
  updated_at timestamp
}

Table subtasks {
  id bigint [pk, increment]
  taskable_type varchar
  taskable_id bigint
  title varchar
  completed boolean [default: false]
  order int [default: 0]
  created_at timestamp
  updated_at timestamp

  indexes {
    (taskable_type, taskable_id)
  }
}

// Document Management
Table document_folders {
  id bigint [pk, increment]
  name varchar
  description text [null]
  parent_id bigint [ref: > document_folders.id, null]
  team_id bigint [ref: > teams.id]
  created_by bigint [ref: > users.id]
  created_at timestamp
  updated_at timestamp
  deleted_at timestamp [null]
}

Table documents {
  id bigint [pk, increment]
  name varchar
  description text [null]
  file_path varchar
  thumbnail_path varchar [null]
  file_type varchar
  file_size bigint
  folder_id bigint [ref: > document_folders.id, null]
  team_id bigint [ref: > teams.id]
  uploaded_by bigint [ref: > users.id]
  access_level enum('public','team','private','specific_users') [default: 'team']
  current_version int [default: 1]
  created_at timestamp
  updated_at timestamp
  deleted_at timestamp [null]
}

Table document_versions {
  id bigint [pk, increment]
  document_id bigint [ref: > documents.id]
  version_number int
  file_path varchar
  thumbnail_path varchar [null]
  file_size bigint
  created_by bigint [ref: > users.id]
  version_note text [null]
  created_at timestamp
  updated_at timestamp

  indexes {
    (document_id, version_number) [unique]
  }
}

Table document_user_permissions {
  id bigint [pk, increment]
  document_id bigint [ref: > documents.id]
  user_id bigint [ref: > users.id]
  created_at timestamp
  updated_at timestamp

  indexes {
    (document_id, user_id) [unique]
  }
}

// Group Chat
Table group_chat_messages {
  id bigint [pk, increment]
  team_id bigint [ref: > teams.id]
  sender_id bigint [ref: > users.id]
  message text [null]
  file_url varchar [null]
  status varchar [default: 'sent']
  client_temp_id varchar [null]
  reply_to_id bigint [ref: > group_chat_messages.id, null]
  created_at timestamp
  updated_at timestamp
}

Table message_reactions {
  id bigint [pk, increment]
  message_id bigint [ref: > group_chat_messages.id]
  user_id bigint [ref: > users.id]
  reaction varchar(10)
  created_at timestamp
  updated_at timestamp

  indexes {
    (message_id, user_id, reaction) [unique]
  }
}

Table message_read_status {
  id bigint [pk, increment]
  message_id bigint [ref: > group_chat_messages.id]
  user_id bigint [ref: > users.id]
  read_at timestamp
}

// Notifications and Devices
Table device_tokens {
  id bigint [pk, increment]
  user_id bigint [ref: > users.id]
  device_id varchar
  token varchar
  device_type varchar
  created_at timestamp
  updated_at timestamp
}

Table notification_settings {
  id bigint [pk, increment]
  user_id bigint [ref: > users.id]
  task_reminders boolean [default: true]
  team_invitations boolean [default: true]
  team_updates boolean [default: true]
  chat_messages boolean [default: true]
  created_at timestamp
  updated_at timestamp
}
