{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite", "watch": "vite build --watch"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "socket.io-client": "^4.7.4", "chart.js": "^4.4.0", "vue-chartjs": "^5.2.0", "date-fns": "^2.30.0", "@headlessui/vue": "^1.7.16", "@heroicons/vue": "^2.0.18"}, "devDependencies": {"@tailwindcss/vite": "^4.0.0", "@vitejs/plugin-vue": "^4.5.0", "axios": "^1.8.2", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.2.0", "tailwindcss": "^4.0.0", "vite": "^6.0.11"}}