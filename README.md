# 📱 Task Management API

## 🎯 Giới thiệu

**Task Management API** là một hệ thống backend hoàn chỉnh được xây dựng bằng Laravel Framework để phục vụ ứng dụng mobile quản lý công việc và chat nhóm. API hỗ trợ cả hoạt động offline và đồng bộ real-time.

## 🚀 Quick Start

### ⚙️ Cài đặt nhanh
```bash
# Clone và setup
git clone <repository-url>
cd API_TaskMobileApp
composer install

# Database setup
cp .env.example .env
php artisan key:generate
php artisan migrate
php artisan db:seed --class=AdminUserSeeder

# Start servers
php artisan serve --host=0.0.0.0 --port=8000
php artisan reverb:start --host=0.0.0.0 --port=8080
```

### 🌐 URLs
- **API Server**: `http://localhost:8000/api/`
- **Admin Panel**: `http://localhost:8000/admin/`
- **WebSocket**: `ws://localhost:8080/ws`

### 👤 Default Admin
```
Email: <EMAIL>
Password: admin123
```

## 📚 Tài liệu đầy đủ

### 📖 Tài liệu chính
- **[PROJECT_DOCUMENTATION.md](PROJECT_DOCUMENTATION.md)** - 📋 Tài liệu tổng hợp đầy đủ
- **[API_DOCUMENTATION.md](API_DOCUMENTATION.md)** - 🌐 Chi tiết tất cả API endpoints
- **[ADMIN_PANEL_SETUP.md](ADMIN_PANEL_SETUP.md)** - 🛡️ Hướng dẫn admin panel

### 🔗 Database Schema
- **[dbdiagram_code.txt](dbdiagram_code.txt)** - 🗄️ ERD database schema

## ✨ Tính năng nổi bật

### 🔐 Authentication & Security
- JWT Token Authentication
- Google OAuth Sign-In
- Biometric Authentication
- Two-Factor Authentication (2FA)
- Multi-device Support

### 📝 Task Management
- **Personal Tasks**: CRUD, categories, priorities, deadlines
- **Team Tasks**: Collaborative task management, assignments
- **Subtasks**: Hierarchical task organization
- **Kanban Board**: Visual task management

### 👥 Team Collaboration
- Team creation & management
- Role-based permissions (Admin/Member)
- Team invitations system
- Real-time team chat with WebSocket
- Document sharing & versioning

### 🔄 Offline & Sync
- **Offline-first**: App hoạt động khi không có mạng
- **5 loại sync**: Initial, Quick, Push, Selective, Conflict Resolution
- **Auto-save drafts**: Tự động lưu khi đang nhập
- **Multi-device sync**: Đồng bộ giữa nhiều thiết bị

### 🛡️ Admin Panel
- System dashboard với thống kê
- User & team management
- Privacy-protected (không xem nội dung riêng tư)
- Responsive design

## 🛠️ Tech Stack

```
Backend:     Laravel 12.0, PHP 8.2+
Database:    MySQL 8.0+
WebSocket:   Laravel Reverb
Auth:        Laravel Sanctum + JWT
Push:        Firebase Cloud Messaging
OAuth:       Google Sign-In
Admin UI:    Bootstrap 5 + Blade
```

## 📱 Android Integration

### 🔌 API Connection
```kotlin
// Base URLs
API_BASE_URL = "http://10.0.2.2:8000/api/"
WEBSOCKET_URL = "ws://10.0.2.2:8080/ws"

// Authentication
Authorization: Bearer {token}
```

### 🔄 Sync Strategy
```kotlin
// 5 types of sync
1. Initial Sync    - First time data load
2. Quick Sync      - Delta changes (every 15 min)
3. Push Sync       - Upload offline changes
4. Selective Sync  - User-chosen data types
5. Conflict Sync   - Resolve data conflicts
```

### 💾 Local Storage
```sql
-- SQLite tables needed
users, personal_tasks, teams, team_tasks
chat_messages, documents, sync_status
pending_actions, drafts
```

## 🌐 API Overview

### 📍 Main Endpoints (217 total routes)
```
Authentication (11):  /api/auth/*
Personal Tasks (7):   /api/personal-tasks/*
Teams (5):           /api/teams/*
Team Chat (8):       /api/teams/{team}/chat/*
Documents (10):      /api/documents/*
Sync (5):           /api/sync/*
Admin Panel:        /admin/*
```

### 🔄 Real-time Events
```javascript
// WebSocket events
new-chat-message
message-read
user-typing
team-invitation-created
message-reaction-updated
```

## 🗄️ Database

### 📊 30+ Tables
```sql
-- Core entities
users, teams, personal_tasks, team_tasks

-- Communication
group_chat_messages, message_reactions

-- Documents
documents, document_folders, document_versions

-- Sync & Offline
sync_status, drafts, devices

-- Admin & System
sessions, cache, jobs, migrations
```

## 🚀 Production Ready

### ✅ Features
- Comprehensive API documentation
- Admin panel for management
- Real-time WebSocket support
- Offline-first architecture
- Multi-device synchronization
- Security & privacy protection
- Performance optimization
- Error handling & logging

### 📈 Scalability
- Horizontal scaling ready
- Database optimization
- Caching strategies
- Queue system support
- Load balancer compatible

## 📞 Support

### 🆘 Getting Help
1. 📖 Check [PROJECT_DOCUMENTATION.md](PROJECT_DOCUMENTATION.md)
2. 🌐 Review [API_DOCUMENTATION.md](API_DOCUMENTATION.md)
3. 🛡️ See [ADMIN_PANEL_SETUP.md](ADMIN_PANEL_SETUP.md)
4. 🧪 Test in development environment

### 🔒 Security
Always prioritize user privacy and data protection!

---

**📱 Ready for Android development!** 🚀

*Last Updated: January 2025 | Version: 1.0.0*
