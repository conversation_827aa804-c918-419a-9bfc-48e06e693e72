<?php

namespace App\Events;

use App\Models\Team;
use App\Models\TeamInvitation;
use App\Models\User;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class TeamInvitationAccepted implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public TeamInvitation $invitation;
    public Team $team;
    public User $user;

    /**
     * Create a new event instance.
     */
    public function __construct(TeamInvitation $invitation, User $user)
    {
        $this->invitation = $invitation;
        $this->team = $invitation->team;
        $this->user = $user;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('teams.' . $this->team->id),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'team.invitation.accepted';
    }

    /**
     * Get the data to broadcast.
     *
     * @return array<string, mixed>
     */
    public function broadcastWith(): array
    {
        return [
            'invitation_id' => $this->invitation->id,
            'team_id' => $this->team->id,
            'team_name' => $this->team->name,
            'new_member' => [
                'id' => $this->user->id,
                'name' => $this->user->name,
                'email' => $this->user->email,
                'avatar' => $this->user->avatar ?? null,
                'role' => $this->invitation->role,
                'joined_at' => now()->toIso8601String()
            ],
            'message' => $this->user->name . ' đã tham gia team ' . $this->team->name,
            'timestamp' => now()->toIso8601String()
        ];
    }
}
