<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Tạo admin user đ<PERSON>u tiên
        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'System Administrator',
                'email' => '<EMAIL>',
                'password' => Hash::make('admin123'),
                'is_admin' => true,
                'email_verified_at' => now(),
            ]
        );

        $this->command->info('Admin user created successfully!');
        $this->command->info('Email: <EMAIL>');
        $this->command->info('Password: admin123');
        $this->command->warn('Please change the password after first login!');
    }
}
